package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.crm.api.domain.CrmFriendArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 好友地区 - 数据层
 */
public interface CrmFriendAreaMapper extends BaseMapper<CrmFriendArea> {

    /**
     * 查询好友地区列表
     */
    List<CrmFriendArea> selectFriendAreaList(CrmFriendArea friendArea);

    /**
     * 根据父ID查询子地区列表
     */
    List<CrmFriendArea> selectFriendAreaByParentId(@Param("parentId") Long parentId);

    /**
     * 查询所有一级地区（父ID为0）
     */
    List<CrmFriendArea> selectMainAreas();

    /**
     * 查询所有二级地区（父ID不为0）
     */
    List<CrmFriendArea> selectSubAreas();
}
