package com.yooa.crm.controller;

import com.yooa.common.core.controller.BaseController;
import com.yooa.common.core.domain.AjaxResult;
import com.yooa.common.core.page.TableDataInfo;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.crm.api.domain.CrmFriendArea;
import com.yooa.crm.service.FriendAreaService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 好友地区 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/friendArea")
public class FriendAreaController extends BaseController {

    private final FriendAreaService friendAreaService;

    /**
     * 查询好友地区列表
     */
    @RequiresPermissions("crm:friendArea:list")
    @GetMapping("/list")
    public TableDataInfo list(CrmFriendArea friendArea) {
        startPage();
        List<CrmFriendArea> list = friendAreaService.selectFriendAreaList(friendArea);
        return getDataTable(list);
    }

    /**
     * 获取好友地区详细信息
     */
    @RequiresPermissions("crm:friendArea:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(friendAreaService.getById(id));
    }

    /**
     * 新增好友地区
     */
    @RequiresPermissions("crm:friendArea:add")
    @Log(title = "好友地区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmFriendArea friendArea) {
        return toAjax(friendAreaService.insertFriendArea(friendArea));
    }

    /**
     * 修改好友地区
     */
    @RequiresPermissions("crm:friendArea:edit")
    @Log(title = "好友地区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmFriendArea friendArea) {
        return toAjax(friendAreaService.updateFriendArea(friendArea));
    }

    /**
     * 删除好友地区
     */
    @RequiresPermissions("crm:friendArea:remove")
    @Log(title = "好友地区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(friendAreaService.deleteFriendAreaByIds(ids));
    }

    /**
     * 根据父ID查询子地区列表
     */
    @GetMapping("/children/{parentId}")
    public AjaxResult getChildren(@PathVariable("parentId") Long parentId) {
        List<CrmFriendArea> list = friendAreaService.selectFriendAreaByParentId(parentId);
        return success(list);
    }

    /**
     * 查询所有一级地区
     */
    @GetMapping("/main")
    public AjaxResult getMainAreas() {
        List<CrmFriendArea> list = friendAreaService.selectMainAreas();
        return success(list);
    }

    /**
     * 查询所有二级地区
     */
    @GetMapping("/sub")
    public AjaxResult getSubAreas() {
        List<CrmFriendArea> list = friendAreaService.selectSubAreas();
        return success(list);
    }
}
