package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmFriendArea;
import com.yooa.crm.mapper.CrmFriendAreaMapper;
import com.yooa.crm.service.FriendAreaService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 好友地区 - 服务实现层
 */
@Service
@AllArgsConstructor
public class FriendAreaServiceImpl extends ServiceImpl<CrmFriendAreaMapper, CrmFriendArea> implements FriendAreaService {

    private final CrmFriendAreaMapper friendAreaMapper;

    @Override
    public List<CrmFriendArea> selectFriendAreaList(CrmFriendArea friendArea) {
        return friendAreaMapper.selectFriendAreaList(friendArea);
    }

    @Override
    public List<CrmFriendArea> selectFriendAreaByParentId(Long parentId) {
        return friendAreaMapper.selectFriendAreaByParentId(parentId);
    }

    @Override
    public List<CrmFriendArea> selectMainAreas() {
        return friendAreaMapper.selectMainAreas();
    }

    @Override
    public List<CrmFriendArea> selectSubAreas() {
        return friendAreaMapper.selectSubAreas();
    }

    @Override
    public int insertFriendArea(CrmFriendArea friendArea) {
        return friendAreaMapper.insert(friendArea);
    }

    @Override
    public int updateFriendArea(CrmFriendArea friendArea) {
        return friendAreaMapper.updateById(friendArea);
    }

    @Override
    public int deleteFriendAreaByIds(Long[] ids) {
        int result = 0;
        for (Long id : ids) {
            result += friendAreaMapper.deleteById(id);
        }
        return result;
    }

    @Override
    public int deleteFriendAreaById(Long id) {
        return friendAreaMapper.deleteById(id);
    }
}
