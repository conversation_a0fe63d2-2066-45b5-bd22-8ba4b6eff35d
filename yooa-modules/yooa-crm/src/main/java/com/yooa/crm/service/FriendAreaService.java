package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmFriendArea;

import java.util.List;

/**
 * 好友地区 - 服务层
 */
public interface FriendAreaService extends IService<CrmFriendArea> {

    /**
     * 查询好友地区列表
     */
    List<CrmFriendArea> selectFriendAreaList(CrmFriendArea friendArea);

    /**
     * 根据父ID查询子地区列表
     */
    List<CrmFriendArea> selectFriendAreaByParentId(Long parentId);

    /**
     * 查询所有一级地区（父ID为0）
     */
    List<CrmFriendArea> selectMainAreas();

    /**
     * 查询所有二级地区（父ID不为0）
     */
    List<CrmFriendArea> selectSubAreas();

    /**
     * 新增好友地区
     */
    int insertFriendArea(CrmFriendArea friendArea);

    /**
     * 修改好友地区
     */
    int updateFriendArea(CrmFriendArea friendArea);

    /**
     * 批量删除好友地区
     */
    int deleteFriendAreaByIds(Long[] ids);

    /**
     * 删除好友地区信息
     */
    int deleteFriendAreaById(Long id);
}
