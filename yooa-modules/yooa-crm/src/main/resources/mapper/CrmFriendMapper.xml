<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmFriendMapper">

    <select id="selectListCustom" resultType="com.yooa.crm.api.domain.vo.CrmCommonFriendVo">
        select
        f.friend_name name,
        f.friend_id friendId,
        cf.customer_id customerId,
        f.sex,
        f.age,
        f.main_area_id,
        f.sub_area_id,
        f.language,
        f.demand,
        f.income_level incomeLevel,
        f.work_type workType,
        v.fans_type maxFansType,
        f.lose_time loseTime,
        f.record_date recordDate,
        f.create_time createTime
        from crm_friend f
        LEFT JOIN crm_customer_friend cf on cf.friend_id = f.friend_id
        LEFT JOIN (select friend_id,extend_id,fans_type,ROW_NUMBER() OVER( PARTITION BY friend_id ORDER BY fans_type DESC) rn from yooa_extend.extend_vermicelli)v on v.friend_id = f.friend_id and f.extend_id = v.extend_id and v.rn = 1
        <where>
             f.status = 2
            AND f.contact_phone IS NOT NULL
            AND f.contact_phone != ''
            <if test="query.area != null and query.area != ''">
                AND f.main_area_id = #{query.area}
            </if>
            <if test="query.workType != null and query.workType != ''">
                AND f.work_type = #{query.workType}
            </if>
            <if test="query.beginAge != null">
                AND f.age >= #{query.beginAge}
            </if>
            <if test="query.endAge != null">
                AND f.age &lt;= #{query.endAge}
            </if>
            <if test="query.sex != null and query.sex != ''">
                AND f.sex = #{query.sex}
            </if>
            <if test="query.language != null and query.language != ''">
                AND f.language = #{query.language}
            </if>
            <if test="query.recordDate != null">
                AND (f.record_date BETWEEN #{query.recordDate[0]} AND #{query.recordDate[1]})
            </if>
            <if test="query.loseDate != null">
                AND (f.lose_time BETWEEN #{query.LoseDate[0]} AND #{query.LoseDate[1]})
            </if>
            <if test="query.keyWord != null and query.keyWord != ''">
                AND f.friend_name like CONCAT('%',#{query.keyWord},'%')
                OR  f.contact_phone = #{query.keyWord}
            </if>
        </where>
        GROUP BY f.friend_id
        ORDER BY f.friend_id DESC
    </select>

    <resultMap id="FriendCustomerVo" type="com.yooa.crm.api.domain.vo.FriendCustomerVo">
        <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <id property="friendId" column="friend_id" jdbcType="BIGINT"/>
        <result property="friendName" column="friend_name" jdbcType="VARCHAR"/>
        <result property="fansUp" column="fansUp" jdbcType="INTEGER"/>
        <result property="fans2h" column="fans2h" jdbcType="INTEGER"/>
        <result property="fans5h" column="fans5h" jdbcType="INTEGER"/>
        <result property="fans5k" column="fans5k" jdbcType="INTEGER"/>
        <result property="fans5w" column="fans5w" jdbcType="INTEGER"/>
        <result property="recordDate" column="record_date" jdbcType="DATE"/>
        <result property="loseTime" column="lose_time" jdbcType="DATE"/>
        <result property="mainChannelName" column="main_Channel_name" jdbcType="VARCHAR"/>
        <result property="subChannelName" column="sub_Channel_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="DATE"/>
        <result property="totalRecharge" column="total_recharge" jdbcType="DECIMAL"/>
        <result property="actualRecharge" column="actual_recharge" jdbcType="DECIMAL"/>
        <result property="channelUserName" column="channel_user_name" jdbcType="VARCHAR"/>
        <result property="channelNickName" column="channel_nick_name" jdbcType="VARCHAR"/>
        <association property="extend" javaType="com.yooa.crm.api.domain.vo.ExtendVo">
            <id property="extendId" column="extend_id" jdbcType="VARCHAR"/>
            <result property="userId" column="extend_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="extend_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="extend_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="extend_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="extend_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="extend_ancestors_names" jdbcType="VARCHAR"/>
        </association>
        <association property="serve" javaType="com.yooa.crm.api.domain.vo.ServeVo">
            <id property="serveId" column="serve_id" jdbcType="BIGINT"/>
            <result property="userId" column="serve_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="serve_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="serve_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="serve_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="serve_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="serve_ancestors_names" jdbcType="VARCHAR"/>
            <result property="receiveTime" column="serve_join_time"/>
            <result property="loseTime" column="serve_lose_time"/>
        </association>
        <collection property="anchorOperate" ofType="com.yooa.crm.api.domain.vo.AnchorOperateVo" column="{customerId = customer_id,extendId = extend_id}"
                    select="selectAnchorOperateVoList"/>
    </resultMap>

    <resultMap id="FriendCustomerExportVo" type="com.yooa.crm.api.domain.vo.FriendCustomerVo">
        <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <id property="friendId" column="friend_id" jdbcType="BIGINT"/>
        <result property="friendName" column="friend_name" jdbcType="VARCHAR"/>
        <result property="fansUp" column="fansUp" jdbcType="INTEGER"/>
        <result property="fans2h" column="fans2h" jdbcType="INTEGER"/>
        <result property="fans5h" column="fans5h" jdbcType="INTEGER"/>
        <result property="fans5k" column="fans5k" jdbcType="INTEGER"/>
        <result property="fans5w" column="fans5w" jdbcType="INTEGER"/>
        <result property="recordDate" column="record_date" jdbcType="DATE"/>
        <result property="mainChannelName" column="main_Channel_name" jdbcType="VARCHAR"/>
        <result property="subChannelName" column="sub_Channel_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="DATE"/>
        <result property="totalRecharge" column="total_recharge" jdbcType="DECIMAL"/>
        <result property="actualRecharge" column="actual_recharge" jdbcType="DECIMAL"/>
        <result property="channelUserName" column="channel_user_name" jdbcType="VARCHAR"/>
        <result property="channelNickName" column="channel_nick_name" jdbcType="VARCHAR"/>
        <association property="extend" javaType="com.yooa.crm.api.domain.vo.ExtendVo">
            <id property="extendId" column="extend_id" jdbcType="BIGINT"/>
            <result property="userId" column="extend_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="extend_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="extend_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="extend_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="extend_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="extend_ancestors_names" jdbcType="VARCHAR"/>
        </association>
        <association property="serve" javaType="com.yooa.crm.api.domain.vo.ServeVo">
            <id property="serveId" column="serve_id" jdbcType="BIGINT"/>
            <result property="userId" column="serve_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="serve_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="serve_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="serve_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="serve_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="serve_ancestors_names" jdbcType="VARCHAR"/>
            <result property="receiveTime" column="serve_join_time"/>
            <result property="loseTime" column="serve_lose_time"/>
        </association>
    </resultMap>

    <resultMap id="FriendCustomerOperateVo" type="com.yooa.crm.api.domain.vo.OperateFriendCustomerVo">
        <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <id property="friendId" column="friend_id" jdbcType="BIGINT"/>
        <result property="friendName" column="friend_name" jdbcType="VARCHAR"/>
        <result property="fansUpReward" column="fansUpReward" jdbcType="INTEGER"/>
        <result property="fansUpRecharge" column="fansUpRecharge" jdbcType="INTEGER"/>
        <result property="fans1h" column="fans1h" jdbcType="INTEGER"/>
        <result property="fans2h" column="fans2h" jdbcType="INTEGER"/>
        <result property="fans5h" column="fans5h" jdbcType="INTEGER"/>
        <result property="fans5k" column="fans5k" jdbcType="INTEGER"/>
        <result property="fans5w" column="fans5w" jdbcType="INTEGER"/>
        <result property="fans10w" column="fans10w" jdbcType="INTEGER"/>
        <result property="receiveTime" column="receive_time" jdbcType="DATE"/>
        <result property="loseTime" column="end_time" jdbcType="DATE"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="DATE"/>
        <result property="totalRecharge" column="total_recharge" jdbcType="DECIMAL"/>
        <result property="actualRecharge" column="actual_recharge" jdbcType="DECIMAL"/>
        <association property="extend" javaType="com.yooa.crm.api.domain.vo.ExtendVo">
            <id property="extendId" column="extend_id" jdbcType="BIGINT"/>
            <result property="userId" column="extend_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="extend_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="extend_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="extend_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="extend_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="extend_ancestors_names" jdbcType="VARCHAR"/>
        </association>
        <association property="serve" javaType="com.yooa.crm.api.domain.vo.ServeVo">
            <id property="serveId" column="serve_id" jdbcType="BIGINT"/>
            <result property="userId" column="serve_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="serve_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="serve_nick_name" jdbcType="VARCHAR"/>
        </association>
        <association property="anchorOperate" javaType="com.yooa.crm.api.domain.vo.AnchorOperateVo" >
            <result property="pyOperateId" column="py_operate_id"/>
            <result property="userId" column="operateId"/>
            <result property="nickName" column="operateNickName"/>
            <result property="ancestorsNames" column="operateAncestorsNames"/>
            <result property="deptName" column="operateDeptName"/>
            <result property="anchorName" column="operateAnchorName"/>
            <result property="anchorId" column="operateAnchorId"/>
            <result property="anchorLanguage" column="anchorLanguage"/>
            <result property="anchorSex" column="anchorSex"/>
        </association>
    </resultMap>

    <resultMap id="RegisterAllVo" type="com.yooa.crm.api.domain.vo.RegisterVo">
        <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <id property="friendId" column="friend_id" jdbcType="BIGINT"/>
        <result property="friendName" column="friend_name" jdbcType="VARCHAR"/>
        <result property="recordDate" column="record_date" jdbcType="DATE"/>
        <result property="loseTime" column="lose_time" jdbcType="DATE"/>
        <result property="mainChannelName" column="main_Channel_name" jdbcType="VARCHAR"/>
        <result property="subChannelName" column="sub_Channel_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="DATE"/>
        <result property="qualityTime" column="quality_time" jdbcType="DATE"/>
        <result property="accountType" column="accountType" jdbcType="VARCHAR"/>
        <result property="channelUserName" column="channel_user_name" jdbcType="VARCHAR"/>
        <result property="channelNickName" column="channel_nick_name" jdbcType="VARCHAR"/>
        <association property="extend" javaType="com.yooa.crm.api.domain.vo.ExtendVo">
            <id property="extendId" column="extend_id" jdbcType="VARCHAR"/>
            <result property="userId" column="extend_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="extend_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="extend_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="extend_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="extend_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="extend_ancestors_names" jdbcType="VARCHAR"/>
        </association>
        <association property="serve" javaType="com.yooa.crm.api.domain.vo.ServeVo">
            <id property="serveId" column="serve_id" jdbcType="BIGINT"/>
            <result property="userId" column="serve_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="serve_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="serve_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="serve_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="serve_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="serve_ancestors_names" jdbcType="VARCHAR"/>
            <result property="receiveTime" column="serve_join_time"/>
            <result property="loseTime" column="serve_lose_time"/>
        </association>
        <collection property="anchorOperate" ofType="com.yooa.crm.api.domain.vo.AnchorOperateVo" column="{customerId = customer_id,extendId = extend_id}"
                    select="selectAnchorOperateVoList"/>
    </resultMap>

    <resultMap id="RegisterAllExportVo" type="com.yooa.crm.api.domain.vo.RegisterVo">
        <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <id property="friendId" column="friend_id" jdbcType="BIGINT"/>
        <result property="friendName" column="friend_name" jdbcType="VARCHAR"/>
        <result property="recordDate" column="record_date" jdbcType="DATE"/>
        <result property="loseTime" column="lose_time" jdbcType="DATE"/>
        <result property="mainChannelName" column="main_Channel_name" jdbcType="VARCHAR"/>
        <result property="subChannelName" column="sub_Channel_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="DATE"/>
        <result property="qualityTime" column="quality_time" jdbcType="DATE"/>
        <result property="accountType" column="accountType" jdbcType="VARCHAR"/>
        <result property="channelUserName" column="channel_user_name" jdbcType="VARCHAR"/>
        <result property="channelNickName" column="channel_nick_name" jdbcType="VARCHAR"/>
        <result property="anchorOperateJSON" column="anchorOperateJSON"/>
        <association property="extend" javaType="com.yooa.crm.api.domain.vo.ExtendVo">
            <id property="extendId" column="extend_id" jdbcType="VARCHAR"/>
            <result property="userId" column="extend_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="extend_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="extend_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="extend_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="extend_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="extend_ancestors_names" jdbcType="VARCHAR"/>
        </association>
        <association property="serve" javaType="com.yooa.crm.api.domain.vo.ServeVo">
            <id property="serveId" column="serve_id" jdbcType="BIGINT"/>
            <result property="userId" column="serve_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="serve_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="serve_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="serve_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="serve_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="serve_ancestors_names" jdbcType="VARCHAR"/>
            <result property="receiveTime" column="serve_join_time"/>
            <result property="loseTime" column="serve_lose_time"/>
        </association>
    </resultMap>

    <resultMap id="RegisterExtendVo" type="com.yooa.crm.api.domain.vo.RegisterVo">
        <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <id property="friendId" column="friend_id" jdbcType="BIGINT"/>
        <result property="friendName" column="friend_name" jdbcType="VARCHAR"/>
        <result property="recordDate" column="record_date" jdbcType="DATE"/>
        <result property="loseTime" column="lose_time" jdbcType="DATE"/>
        <result property="mainChannelName" column="main_Channel_name" jdbcType="VARCHAR"/>
        <result property="subChannelName" column="sub_Channel_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="DATE"/>
        <result property="qualityTime" column="quality_time" jdbcType="DATE"/>
        <result property="accountType" column="accountType" jdbcType="VARCHAR"/>
        <result property="channelUserName" column="channel_user_name" jdbcType="VARCHAR"/>
        <result property="channelNickName" column="channel_nick_name" jdbcType="VARCHAR"/>
        <association property="extend" javaType="com.yooa.crm.api.domain.vo.ExtendVo">
            <id property="extendId" column="extend_id" jdbcType="VARCHAR"/>
            <result property="userId" column="extend_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="extend_user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="extend_nick_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="extend_dept_id" jdbcType="BIGINT"/>
            <result property="deptName" column="extend_dept_name" jdbcType="VARCHAR"/>
            <result property="ancestorsNames" column="extend_ancestors_names" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <!--好友及扩展信息字段-->
    <sql id="FriendReceiveRecordDtoSql_f">
        f.friend_id, f.friend_name, f.contact_mode, f.contact_phone, f.age, f.sex, f.main_area_id, f.sub_area_id, f.matrimony, f.demand, f.work_type,
        f.language, f.status, f.income_level, f.projected_consumption, f.remark,main_channel_id, f.sub_channel_id, f.pitcher_id,
        f.lose_time, f.record_date,  f.extend_id, f.fans_type,
        f.material_type, f.rest_time, f.play_way, f.concern_town_talk, f.sensitive_town_talk, f.concern_app,
        f.create_by AS f_create_by, f.create_time AS f_create_time, f.update_by AS f_update_by, f.update_time AS f_update_time,
        u2.user_name AS receive_user_name, u2.nick_name AS receive_nick_name, d2.dept_id AS receive_dept_id, d2.dept_name AS receive_dept_name, d2.ancestors_names AS receive_ancestors_names,
        u4.user_name AS channel_user_name, u4.nick_name AS channel_nick_name, fc1.channel_name AS main_channel_name, fc2.channel_name AS sub_channel_name,
        fa1.area AS main_area_name, fa2.area AS sub_area_name
    </sql>

    <resultMap id="CustomerVoMap" type="com.yooa.crm.api.domain.vo.FriendDetailsVo">
        <id property="friendId" column="friend_id" />
        <result property="friendName" column="friend_name" />
        <result property="contactMode" column="contact_mode" />
        <result property="contactPhone" column="contact_phone" />
        <result property="age" column="age" />
        <result property="sex" column="sex" />
        <result property="mainAreaId" column="main_area_id" />
        <result property="subAreaId" column="sub_area_id" />
        <result property="matrimony" column="matrimony" />
        <result property="demand" column="demand" />
        <result property="workType" column="work_type" />
        <result property="language" column="language" />
        <result property="status" column="status" />
        <result property="incomeLevel" column="income_level" />
        <result property="projectedConsumption" column="projected_consumption" />
        <result property="remark" column="remark" />
        <result property="mainChannelId" column="main_channel_id" />
        <result property="subChannelId" column="sub_channel_id" />
        <result property="pitcherId" column="pitcher_id" />
        <result property="receiveNumber" column="receive_number" />
        <result property="loseTime" column="lose_time" jdbcType="TIMESTAMP"/>
        <result property="recordDate" column="record_date" jdbcType="DATE"/>
        <result property="extendId" column="extend_id" />
        <result property="fansType" column="fans_type" />
        <result property="createBy" column="f_create_by"/>
        <result property="createTime" column="f_create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="f_update_by" />
        <result property="updateTime" column="f_update_time"  jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" />

        <result property="channelUserName" column="channel_user_name" jdbcType="VARCHAR"/>
        <result property="channelNickName" column="channel_nick_name" jdbcType="VARCHAR"/>
        <result property="mainChannelName" column="main_channel_name" jdbcType="VARCHAR"/>
        <result property="subChannelName" column="sub_channel_name" jdbcType="VARCHAR"/>
        <result property="receiveUserName" column="receive_user_name" jdbcType="VARCHAR"/>
        <result property="receiveNickName" column="receive_nick_name" jdbcType="VARCHAR"/>
        <result property="receiveDeptId" column="receive_dept_id" jdbcType="BIGINT"/>
        <result property="receiveDeptName" column="receive_dept_name" jdbcType="VARCHAR"/>
        <result property="receiveAncestorsNames" column="receive_ancestors_names" jdbcType="VARCHAR"/>

        <!-- 新增字段映射 -->
        <result property="materialType" column="material_type" jdbcType="BIGINT"/>
        <result property="restTime" column="rest_time" jdbcType="VARCHAR"/>
        <result property="playWay" column="play_way" jdbcType="VARCHAR"/>
        <result property="concernTownTalk" column="concern_town_talk" jdbcType="VARCHAR"/>
        <result property="sensitiveTownTalk" column="sensitive_town_talk" jdbcType="VARCHAR"/>
        <result property="concernApp" column="concern_app" jdbcType="VARCHAR"/>
        <result property="mainAreaName" column="main_area_name" jdbcType="VARCHAR"/>
        <result property="subAreaName" column="sub_area_name" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectAnchorOperateVoList" resultType="com.yooa.crm.api.domain.vo.AnchorOperateVo">
        SELECT
            ao.operate_id,
            a.anchor_id,
            a.anchor_name,
            u.user_id,
            u.user_name,
            u.nick_name,
            d.dept_name,
            d.ancestors_names,
            cja.join_time AS receive_time
        FROM
            crm_customer_join_anchor cja
                LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
                LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
                LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
                LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        WHERE
            cja.status = 1
            AND cja.customer_id = #{customerId}
            AND cja.extend_id = #{extendId}
    </select>

    <select id="selectRecharge" resultType="com.yooa.crm.api.domain.vo.FriendCustomerVo">
        SELECT
            f.customer_id,
            f.friend_id,
            IFNULL(SUM(b.order_money), 0)                                                    AS total_recharge
            <choose>
                <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                    ,IFNULL(SUM(CASE WHEN b.order_time >= #{query.rechargeBeginTime} AND
                    b.order_time &lt;= #{query.rechargeEndTime} THEN order_money ELSE 0 END), 0)
                    AS actual_recharge
                </when>
                <otherwise>
                    ,IFNULL(SUM(b.order_money), 0)                                           AS actual_recharge
                </otherwise>
            </choose>
        FROM
        (
            SELECT
                cf.*,
                (   SELECT
                        GROUP_CONCAT(pd_user_id) AS py_extend_ids
                    FROM
                        yooa_system.sys_user_pd
                    where
                        user_id = f.extend_id AND type = 1
                    GROUP BY user_id
                ) AS py_extend_ids
            FROM crm_customer_friend AS cf
            LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
            WHERE
            <foreach collection="list" index="index" item="item" separator="OR">
                (cf.customer_id = #{item.customerId} AND cf.friend_id = #{item.friendId})
            </foreach>
        ) AS f
        LEFT JOIN (
            SELECT
                customer_id,
                IFNULL(SUM(order_money),0)      AS order_money,
                DATE(order_time)                AS order_time,
                extend_id
            FROM
                crm_customer_order
            WHERE
                order_status = 1
            GROUP BY customer_id,extend_id,DATE(order_time)
        ) b ON f.customer_id = b.customer_id AND b.extend_id = f.create_by
        GROUP BY f.friend_id,f.customer_id
    </select>

    <select id="selectOperateRecharge" resultMap="FriendCustomerOperateVo">
        SELECT
            r.customer_id,
            r.anchor_id                                                                  AS operateAnchorId,
            r.operate_id                                                                 AS py_operate_id,
            IFNULL(SUM(r.total_amount), 0)                                               AS total_recharge
            <choose>
                <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                    ,IFNULL(SUM(CASE WHEN r.add_time >= #{query.rechargeBeginTime} AND
                    r.add_time &lt;= #{query.rechargeEndTime} THEN total_amount ELSE 0 END), 0)
                                                                                         AS actual_recharge
                </when>
                <otherwise>
                    ,IFNULL(SUM(r.total_amount), 0)                                      AS actual_recharge
                </otherwise>
            </choose>
        FROM
            crm_customer_reward r
        WHERE
             customer_id IN
            <foreach collection="customerIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND operate_id IN
            <foreach collection="pyOperateIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND anchor_id IN
            <foreach collection="anchorIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY customer_id,operate_id,anchor_id
    </select>

    <select id="selectFansList" resultType="com.yooa.crm.api.domain.vo.FriendFansVo">
        SELECT
            IFNULL(SUM(CASE WHEN ev.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fansUp,
            IFNULL(SUM(CASE WHEN ev.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans2h,
            IFNULL(SUM(CASE WHEN ev.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans5h,
            IFNULL(SUM(CASE WHEN ev.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5k,
            IFNULL(SUM(CASE WHEN ev.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5w,
            ev.friend_id
        <if test="query.userType == 1 or query.userType == 8">
            ,ev.extend_id AS user_id
        </if>
        <if test="query.userType == 7">
            ,ev.serve_id AS user_id
        </if>
        FROM
            yooa_extend.extend_vermicelli AS ev
        WHERE
            (
            ev.friend_id IN
                <foreach collection="query.friendIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            OR
            <if test="query.userType == 1 or query.userType == 8">
                ev.extend_id
            </if>
            <if test="query.userType == 7">
                ev.serve_id
            </if>
             IN
                <foreach collection="query.receiveIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
            <if test="query.userType == 1">
                AND ev.extend_id IS NOT NULL
            </if>
            <if test="query.userType == 7">
                AND ev.serve_id IS NOT NULL
            </if>
            <if test="query.fansBeginTime != null ">
                AND record_date >= #{query.fansBeginTime}
            </if>
            <if test="query.fansEndTime != null ">
                AND record_date &lt;= #{query.fansEndTime}
            </if>
        GROUP BY
            <if test="query.userType == 1">
                ev.extend_id,
            </if>
            <if test="query.userType == 7">
                ev.serve_id,
            </if>
            ev.friend_id
    </select>

    <select id="selectOperateFansList" resultMap="FriendCustomerOperateVo">
        SELECT
            IFNULL(SUM(CASE WHEN ov.fans_type = 6 THEN 1 ELSE 0 END), 0) AS fansUp,
            IFNULL(SUM(CASE WHEN ov.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans1h,
            IFNULL(SUM(CASE WHEN ov.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans2h,
            IFNULL(SUM(CASE WHEN ov.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5h,
            IFNULL(SUM(CASE WHEN ov.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5k,
            IFNULL(SUM(CASE WHEN ov.fans_type = 4 THEN 1 ELSE 0 END), 0) AS fans5w,
            IFNULL(SUM(CASE WHEN ov.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fans10w,
            ov.operate_id                                                AS operateId,
            ov.friend_id
        FROM
            yooa_extend.operate_vermicelli AS ov
        WHERE
            (
                ov.friend_id IN
                <foreach collection="query.friendIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="query.operateIds != null and query.operateIds.size > 0">
                    OR
                    ov.operate_id IN
                    <foreach collection="query.operateIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            )
            <if test="query.fansBeginTime != null ">
                AND record_date >= #{query.fansBeginTime}
            </if>
            <if test="query.fansEndTime != null ">
                AND record_date &lt;= #{query.fansEndTime}
            </if>
        GROUP BY
            ov.operate_id,ov.friend_id
    </select>

    <select id="selectFriendCustomerList" resultMap="FriendCustomerVo">
        SELECT c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.record_date                                                                    AS record_Date,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS main_channel_name,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = sub_channel_id)  AS sub_channel_name,
            f.create_time                                                                    AS create_time,
            cf.begin_time                                                                    AS update_time,
            cf.py_extend_id                                                                  AS extend_id,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            <if test="(query.beginMoney != null or query.endMoney != null) or query.beginTotalMoney != null or query.endTotalMoney != null">
                IFNULL(SUM(b.order_money), 0)                                                AS total_recharge
                <choose>
                    <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                        ,IFNULL(SUM(CASE WHEN b.order_time >= #{query.rechargeBeginTime} AND
                        b.order_time &lt;= #{query.rechargeEndTime} THEN order_money ELSE 0 END), 0)
                        AS actual_recharge,
                    </when>
                    <otherwise>
                        ,IFNULL(SUM(b.order_money), 0)                                       AS actual_recharge,
                    </otherwise>
                </choose>
            </if>
            <if test="query.type != null or !query.sizeBl">
                fansUp                                                                   AS fansUp,
                fans2h                                                                   AS fans2h,
                fans5h                                                                   AS fans5h,
                fans5k                                                                   AS fans5k,
                fans5w                                                                   AS fans5w,
            </if>
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            s.serve_user_id                                                                  AS serve_user_id,
            s.serve_user_name                                                                AS serve_user_name,
            s.serve_nick_name                                                                AS serve_nick_name,
            s.serve_dept_id                                                                  AS serve_dept_id,
            s.serve_dept_name                                                                AS serve_dept_name,
            s.serve_ancestors_names                                                          AS serve_ancestors_names,
            s.join_time                                                                      AS serve_join_time,
            s.lose_time                                                                      AS serve_lose_time,
            u2.user_name                                                                     AS channel_user_name,
            u2.nick_name                                                                     AS channel_nick_name,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                f.main_channel_id,
                f.sub_channel_id,
                f.pitcher_id,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
            (
                SELECT
                    GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                    user_id
                FROM yooa_system.sys_user_pd
                GROUP BY user_id
            ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                AND (f.type = 1 OR f.type is null
                OR (f.type = 2 AND f.polling_type = 1 AND EXISTS (
                SELECT 1
                FROM crm_customer_friend pollingcf
                WHERE
                pollingcf.friend_id = f.friend_id
                )))
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND f.record_date >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND f.record_date &lt;= #{query.recordEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.subChannelId != null ">
                    AND f.sub_channel_id = #{query.subChannelId}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.mainChannelId != null ">
                    AND f.main_channel_id = #{query.mainChannelId}
                </if>
                <if test="query.channelUserId != null ">
                    AND f.pitcher_id = #{query.channelUserId}
                    AND f.pitcher_id IS NOT NULL
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    <if test="query.userType == 1 ">
                        AND f.extend_id = #{query.userId}
                    </if>
                    <if test="query.userType == 8 ">
                        AND f.pitcher_id IS NOT NULL
                        AND f.pitcher_id = #{query.userId}
                    </if>
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                    <if test="query.userType == 8">
                        AND f.pitcher_id IS NOT NULL
                    </if>

                    <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                        AND f.extend_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                        AND f.pitcher_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--几百粉查询条件-->
        <if test="query.type != null or !query.sizeBl">
            LEFT JOIN (
                SELECT
                    IFNULL(SUM(CASE WHEN ev.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fansUp,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans2h,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans5h,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5k,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5w,
                    ev.friend_id
                    <if test="query.userType == 1 or query.userType == 8">
                        ,ev.extend_id AS user_id
                    </if>
                    <if test="query.userType == 7">
                        ,ev.serve_id AS user_id
                    </if>
                FROM yooa_extend.extend_vermicelli AS ev
                <where>
                    <if test="query.fansBeginTime != null ">
                        record_date >= #{query.fansBeginTime}
                    </if>
                    <if test="query.fansEndTime != null ">
                        AND record_date &lt;= #{query.fansEndTime}
                    </if>
                </where>
                GROUP BY
                    <if test="query.userType == 1 or query.userType == 8">
                        ev.extend_id,
                    </if>
                    <if test="query.userType == 7">
                        ev.serve_id,
                    </if>
                    ev.friend_id
            ) AS ev ON ev.friend_id = f.friend_id AND ev.user_id = f.extend_id
        </if>
        <!--运营查询条件-->
        <if test="query.searchType == 3 or query.status == 2">
        RIGHT JOIN (
            SELECT
                a.anchor_name,
                u.user_id,
                u.user_name,
                u.nick_name,
                cja.extend_id,
                cja.customer_id
            FROM
                crm_customer_join_anchor cja
            LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
            LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
            LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
            LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
            <where>
                cja.status = 1
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
            </where>
            GROUP BY cja.customer_id,cja.extend_id
        ) AS cja ON cja.customer_id = c.customer_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids )
        </if>
        <if test="(query.beginMoney != null or query.endMoney != null) or query.beginTotalMoney != null or query.endTotalMoney != null">
            LEFT JOIN (
                SELECT
                    customer_id,
                    IFNULL(SUM(order_money),0)    AS order_money,
                    DATE(order_time)           AS order_time,
                    extend_id
                FROM
                    crm_customer_order
                WHERE
                    order_status = 1
                GROUP BY customer_id,extend_id,DATE(order_time)
            ) b ON c.customer_id = b.customer_id AND b.extend_id = f.extend_id
        </if>
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                s.*,
                u1.user_id                                                                       AS serve_user_id,
                u1.user_name                                                                     AS serve_user_name,
                u1.nick_name                                                                     AS serve_nick_name,
                d1.dept_id                                                                       AS serve_dept_id,
                d1.dept_name                                                                     AS serve_dept_name,
                d1.ancestors_names                                                               AS serve_ancestors_names
            FROM
            (
                SELECT s.*
                FROM crm_customer_join_serve AS s
                WHERE
                    s.status = 1
                    AND s.id = (
                    SELECT MAX(id)
                    FROM crm_customer_join_serve
                    WHERE customer_id = s.customer_id AND extend_id = s.extend_id AND status = 1
                    )
                    <!--权限范围搜索-->
                    <!--个人权限-->
                    <if test="query.dataScope == 0 ">
                        <if test="query.userType == 7 ">
                            AND s.serve_id IN (SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id = #{query.userId})
                        </if>
                    </if>
                    <!--下级权限-->
                    <if test="query.dataScope == 1 ">
                        <if test="query.userType == 7 and userIds != null and userIds.size() > 0">
                            AND s.serve_id IN
                            (
                            SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id IN
                            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            )
                        </if>
                    </if>
            ) s
            LEFT JOIN yooa_system.sys_user_pd up1 ON s.serve_id = up1.pd_user_id
            LEFT JOIN yooa_system.sys_user u1 ON up1.user_id = u1.user_id
            LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 1 ">
                    AND ev.fansUp > 0
                </if>
                <if test="query.type == 2 ">
                    AND ev.fans2h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ev.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ev.fans5w > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND (extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
                OR channel_nick_name LIKE concat('%', #{query.searchBox}, '%'))
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <select id="selectFriendCustomerNewList" resultMap="FriendCustomerVo">
        SELECT c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.record_date                                                                    AS record_Date,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS main_channel_name,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = sub_channel_id)  AS sub_channel_name,
            f.create_time                                                                    AS create_time,
            cf.begin_time                                                                    AS update_time,
            cf.py_extend_id                                                                  AS extend_id,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            <if test="query.beginMoney != null or query.endMoney != null or query.beginTotalMoney != null or query.endTotalMoney != null">
                IFNULL(SUM(b.order_money), 0)                                                AS total_recharge
                <choose>
                    <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                        ,IFNULL(SUM(CASE WHEN b.order_time >= #{query.rechargeBeginTime} AND
                        b.order_time &lt;= #{query.rechargeEndTime} THEN order_money ELSE 0 END), 0)
                        AS actual_recharge,
                    </when>
                    <otherwise>
                        ,IFNULL(SUM(b.order_money), 0)                                       AS actual_recharge,
                    </otherwise>
                </choose>
            </if>
            <if test="query.type != null or !query.sizeBl">
                fansUp                                                                   AS fansUp,
                fans2h                                                                   AS fans2h,
                fans5h                                                                   AS fans5h,
                fans5k                                                                   AS fans5k,
                fans5w                                                                   AS fans5w,
            </if>
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            s.serve_user_id                                                                  AS serve_user_id,
            s.serve_user_name                                                                AS serve_user_name,
            s.serve_nick_name                                                                AS serve_nick_name,
            s.serve_dept_id                                                                  AS serve_dept_id,
            s.serve_dept_name                                                                AS serve_dept_name,
            s.serve_ancestors_names                                                          AS serve_ancestors_names,
            s.join_time                                                                      AS serve_join_time,
            s.lose_time                                                                      AS serve_lose_time,
            u2.user_name                                                                     AS channel_user_name,
            u2.nick_name                                                                     AS channel_nick_name,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                f.main_channel_id,
                f.sub_channel_id,
                f.pitcher_id,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
            (
                SELECT
                    GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                    user_id
                FROM yooa_system.sys_user_pd
                GROUP BY user_id
            ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND f.record_date >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND f.record_date &lt;= #{query.recordEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.subChannelId != null ">
                    AND f.sub_channel_id = #{query.subChannelId}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.mainChannelId != null ">
                    AND f.main_channel_id = #{query.mainChannelId}
                </if>
                <if test="query.channelUserId != null ">
                    AND f.pitcher_id = #{query.channelUserId}
                    AND f.pitcher_id IS NOT NULL
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    <if test="query.userType == 1 ">
                        AND f.extend_id = #{query.userId}
                    </if>
                    <if test="query.userType == 8 ">
                        AND f.pitcher_id IS NOT NULL
                        AND f.pitcher_id = #{query.userId}
                    </if>
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                    <if test="query.userType == 8">
                        AND f.pitcher_id IS NOT NULL
                    </if>

                    <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                        AND f.extend_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                        AND f.pitcher_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--几百粉查询条件-->
        <if test="query.type != null or !query.sizeBl">
            LEFT JOIN (
                SELECT
                    IFNULL(SUM(CASE WHEN ev.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fansUp,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans2h,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans5h,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5k,
                    IFNULL(SUM(CASE WHEN ev.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5w,
                    ev.friend_id
                    <if test="query.userType == 1 or query.userType == 8">
                        ,ev.extend_id AS user_id
                    </if>
                    <if test="query.userType == 7">
                        ,ev.serve_id AS user_id
                    </if>
                FROM yooa_extend.extend_vermicelli AS ev
                <where>
                    <if test="query.fansBeginTime != null ">
                        record_date >= #{query.fansBeginTime}
                    </if>
                    <if test="query.fansEndTime != null ">
                        AND record_date &lt;= #{query.fansEndTime}
                    </if>
                </where>
                GROUP BY
                    <if test="query.userType == 1 or query.userType == 8">
                        ev.extend_id,
                    </if>
                    <if test="query.userType == 7">
                        ev.serve_id,
                    </if>
                    ev.friend_id
            ) AS ev ON ev.friend_id = f.friend_id AND ev.user_id = f.extend_id
        </if>
        <!--运营查询条件-->
        <if test="query.searchType == 3 or query.status == 2">
        RIGHT JOIN (
            SELECT
                a.anchor_name,
                u.user_id,
                u.user_name,
                u.nick_name,
                cch.handover_id,
                cch.extend_id,
                cch.customer_id
            FROM
                (
                    SELECT
                        handover_id,
                        py_operate_id,
                        py_extend_id,
                        py_anchor_id,
                        operate_id,
                        extend_id,
                        customer_id
                    FROM crm_customer_handover
                    WHERE
                        handover_type = 1
                        AND handover_status = 1
                ) cch
            LEFT JOIN crm_anchor a ON a.anchor_id = cch.py_anchor_id
            LEFT JOIN yooa_system.sys_user u ON u.user_id = cch.operate_id
            <where>
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
            </where>
            GROUP BY cch.handover_id
        ) AS cja ON cja.customer_id = c.customer_id AND cja.extend_id = f.extend_id
        </if>
        <if test="query.beginMoney != null or query.endMoney != null or query.beginTotalMoney != null or query.endTotalMoney != null">
            LEFT JOIN (
                SELECT
                    customer_id,
                    IFNULL(SUM(order_money),0)    AS order_money,
                    DATE(order_time)           AS order_time,
                    extend_id
                FROM
                    crm_customer_order
                WHERE
                    order_status = 1
                GROUP BY customer_id,extend_id,DATE(order_time)
            ) b ON c.customer_id = b.customer_id AND b.extend_id = f.extend_id
        </if>
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                cch.handover_id,
                cch.receive_time                                                                 AS join_time,
                cch.lose_time                                                                    AS lose_time,
                cch.customer_id                                                                  AS customer_id,
                cch.extend_id                                                                    AS extend_id,
                u.user_id                                                                        AS serve_user_id,
                u.user_name                                                                      AS serve_user_name,
                u.nick_name                                                                      AS serve_nick_name,
                d.dept_id                                                                        AS serve_dept_id,
                d.dept_name                                                                      AS serve_dept_name,
                d.ancestors_names                                                                AS serve_ancestors_names
            FROM
            (
                SELECT
                    handover_id,
                    py_serve_id,
                    py_extend_id,
                    serve_id,
                    extend_id,
                    receive_time,
                    lose_time,
                    customer_id
                FROM crm_customer_handover
                WHERE
                    handover_type = 2
                    AND handover_status = 1
                    <!--权限范围搜索-->
                    <!--个人权限-->
                    <if test="query.dataScope == 0 ">
                        <if test="query.userType == 7 ">
                            AND serve_id = #{query.userId}
                        </if>
                    </if>
                    <!--下级权限-->
                    <if test="query.dataScope == 1 ">
                        <if test="query.userType == 7 and userIds != null and userIds.size() > 0">
                            AND serve_id IN
                            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </if>
            ) cch
            LEFT JOIN yooa_system.sys_user u ON cch.serve_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
            GROUP BY cch.handover_id
        ) s ON c.customer_id = s.customer_id AND s.extend_id = f.extend_id
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 1 ">
                    AND ev.fansUp > 0
                </if>
                <if test="query.type == 2 ">
                    AND ev.fans2h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ev.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ev.fans5w > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND (extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
                OR channel_nick_name LIKE concat('%', #{query.searchBox}, '%'))
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <!--和查询接口无差别,resultMap映射配置不一样(不需要查出运营数据)-->
    <select id="exportFriendCustomerList" resultMap="FriendCustomerExportVo">
        SELECT c.customer_id                                                                    AS customer_id,
        f.friend_id                                                                      AS friend_id,
        f.friend_name                                                                    AS friend_name,
        f.record_date                                                                    AS record_date,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS main_channel_name,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = sub_channel_id)  AS sub_channel_name,
        f.create_time                                                                    AS create_time,
        cf.begin_time                                                                    AS update_time,
        cf.py_extend_id                                                                  AS extend_id,
        c.serve_id                                                                       AS serve_id,
        c.last_login_time                                                                AS last_login_time,
        IFNULL(SUM(b.order_money), 0)                                                AS total_recharge
        <choose>
            <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                ,IFNULL(SUM(CASE WHEN b.order_time >= #{query.rechargeBeginTime} AND
                b.order_time &lt;= #{query.rechargeEndTime} THEN order_money ELSE 0 END), 0)
                AS actual_recharge,
            </when>
            <otherwise>
                ,IFNULL(SUM(b.order_money), 0)                                       AS actual_recharge,
            </otherwise>
        </choose>
        fansUp                                                                           AS fansUp,
        fans2h                                                                           AS fans2h,
        fans5h                                                                           AS fans5h,
        fans5k                                                                           AS fans5k,
        fans5w                                                                           AS fans5w,
        u.user_id                                                                        AS extend_user_id,
        u.user_name                                                                      AS extend_user_name,
        u.nick_name                                                                      AS extend_nick_name,
        d.dept_id                                                                        AS extend_dept_id,
        d.dept_name                                                                      AS extend_dept_name,
        d.ancestors_names                                                                AS extend_ancestors_names,
        s.serve_user_id                                                                  AS serve_user_id,
        s.serve_user_name                                                                AS serve_user_name,
        s.serve_nick_name                                                                AS serve_nick_name,
        s.serve_dept_id                                                                  AS serve_dept_id,
        s.serve_dept_name                                                                AS serve_dept_name,
        s.serve_ancestors_names                                                          AS serve_ancestors_names,
        s.join_time                                                                      AS serve_join_time,
        s.lose_time                                                                      AS serve_lose_time,
        u2.user_name                                                                     AS channel_user_name,
        u2.nick_name                                                                     AS channel_nick_name,
        CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
        SELECT
        f.friend_id,
        f.friend_name,
        f.extend_id,
        f.create_time,
        f.record_date,
        f.main_channel_id,
        f.sub_channel_id,
        f.pitcher_id,
        up.py_extend_ids
        FROM crm_friend AS f
        LEFT JOIN
        (
        SELECT
        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
        user_id
        FROM yooa_system.sys_user_pd
        GROUP BY user_id
        ) up ON up.user_id = f.extend_id
        <where>
            f.extend_id IS NOT NULL
            AND (f.type = 1 OR f.type is null
            OR (f.type = 2 AND f.polling_type = 1 AND EXISTS (
            SELECT 1
            FROM crm_customer_friend pollingcf
            WHERE
            pollingcf.friend_id = f.friend_id
            )))
            <if test="query.createBeginTime != null ">
                AND f.create_time >= #{query.createBeginTime}
            </if>
            <if test="query.createEndTime != null ">
                AND f.create_time &lt;= #{query.createEndTime}
            </if>
            <if test="query.recordBeginTime != null ">
                AND f.record_date >= #{query.recordBeginTime}
            </if>
            <if test="query.recordEndTime != null ">
                AND f.record_date &lt;= #{query.recordEndTime}
            </if>
            <if test="query.beginAge != null ">
                AND f.age >= #{query.beginAge}
            </if>
            <if test="query.endAge != null ">
                AND f.age &lt;= #{query.endAge}
            </if>
            <if test="query.subChannelId != null ">
                AND f.sub_channel_id = #{query.subChannelId}
            </if>
            <if test="query.area != null ">
                AND f.area = #{query.area}
            </if>
            <if test="query.language != null ">
                AND f.language = #{query.language}
            </if>
            <if test="query.fansType != null ">
                AND f.fans_type = #{query.fansType}
            </if>
            <if test="query.workType != null ">
                AND f.work_type = #{query.workType}
            </if>
            <if test="query.demand != null ">
                AND f.demand = #{query.demand}
            </if>
            <if test="query.sex != null ">
                AND f.sex = #{query.sex}
            </if>
            <if test="query.mainChannelId != null ">
                AND f.main_channel_id = #{query.mainChannelId}
            </if>
            <if test="query.channelUserId != null ">
                AND f.pitcher_id = #{query.channelUserId}
                AND f.pitcher_id IS NOT NULL
            </if>
            <if test="query.receiveId != null ">
                AND f.extend_id = #{query.receiveId}
            </if>
            <!--权限范围搜索-->
            <!--个人权限-->
            <if test="query.dataScope == 0 ">
                <if test="query.userType == 1 ">
                    AND f.extend_id = #{query.userId}
                </if>
                <if test="query.userType == 8 ">
                    AND f.pitcher_id IS NOT NULL
                    AND f.pitcher_id = #{query.userId}
                </if>
            </if>
            <!--下级权限-->
            <if test="query.dataScope == 1">
                <if test="query.userType == 8">
                    AND f.pitcher_id IS NOT NULL
                </if>

                <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                    AND f.extend_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                    AND f.pitcher_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

            </if>
        </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--几百粉查询条件-->
        <if test="query.type != null or !query.sizeBl">
            LEFT JOIN (
            SELECT
            IFNULL(SUM(CASE WHEN ev.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fansUp,
            IFNULL(SUM(CASE WHEN ev.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans2h,
            IFNULL(SUM(CASE WHEN ev.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans5h,
            IFNULL(SUM(CASE WHEN ev.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5k,
            IFNULL(SUM(CASE WHEN ev.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5w,
            ev.friend_id
            <if test="query.userType == 1 or query.userType == 8">
                ,ev.extend_id AS user_id
            </if>
            <if test="query.userType == 7">
                ,ev.serve_id AS user_id
            </if>
            FROM yooa_extend.extend_vermicelli AS ev
            <where>
                <if test="query.fansBeginTime != null ">
                    record_date >= #{query.fansBeginTime}
                </if>
                <if test="query.fansEndTime != null ">
                    AND record_date &lt;= #{query.fansEndTime}
                </if>
            </where>
            GROUP BY
            <if test="query.userType == 1 or query.userType == 8">
                ev.extend_id,
            </if>
            <if test="query.userType == 7">
                ev.serve_id,
            </if>
            ev.friend_id
            ) AS ev ON ev.friend_id = f.friend_id AND ev.user_id = f.extend_id
        </if>
        <!--运营查询条件-->
        <if test="query.searchType == 3 or query.status == 2">
            RIGHT JOIN (
            SELECT
            a.anchor_name,
            u.user_id,
            u.user_name,
            u.nick_name,
            cja.extend_id,
            cja.customer_id
            FROM
            crm_customer_join_anchor cja
            LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
            LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
            LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
            LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
            <where>
                cja.status = 1
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
            </where>
            GROUP BY cja.customer_id,cja.extend_id
            ) AS cja ON cja.customer_id = c.customer_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids )
        </if>
        LEFT JOIN (
            SELECT
            customer_id,
            IFNULL(SUM(order_money),0)    AS order_money,
            DATE(order_time)           AS order_time,
            extend_id
            FROM
            crm_customer_order
            WHERE
            order_status = 1
            GROUP BY customer_id,extend_id,DATE(order_time)
        ) b ON c.customer_id = b.customer_id AND b.extend_id = f.extend_id
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
        SELECT
        s.*,
        u1.user_id                                                                       AS serve_user_id,
        u1.user_name                                                                     AS serve_user_name,
        u1.nick_name                                                                     AS serve_nick_name,
        d1.dept_id                                                                       AS serve_dept_id,
        d1.dept_name                                                                     AS serve_dept_name,
        d1.ancestors_names                                                               AS serve_ancestors_names
        FROM
        (
        SELECT s.*
        FROM crm_customer_join_serve AS s
        WHERE
        s.status = 1
        AND s.id = (
        SELECT MAX(id)
        FROM crm_customer_join_serve
        WHERE customer_id = s.customer_id AND extend_id = s.extend_id AND status = 1
        )
        <!--权限范围搜索-->
        <!--个人权限-->
        <if test="query.dataScope == 0 ">
            <if test="query.userType == 7 ">
                AND s.serve_id IN (SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id = #{query.userId})
            </if>
        </if>
        <!--下级权限-->
        <if test="query.dataScope == 1 ">
            <if test="query.userType == 7 and userIds != null and userIds.size() > 0">
                AND s.serve_id IN
                (
                SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id IN
                <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        ) s
        LEFT JOIN yooa_system.sys_user_pd up1 ON s.serve_id = up1.pd_user_id
        LEFT JOIN yooa_system.sys_user u1 ON up1.user_id = u1.user_id
        LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        <where>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
        </where>
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 1 ">
                    AND ev.fansUp > 0
                </if>
                <if test="query.type == 2 ">
                    AND ev.fans2h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ev.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ev.fans5w > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND (extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
                OR channel_nick_name LIKE concat('%', #{query.searchBox}, '%'))
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <select id="exportFriendCustomerNewList" resultMap="FriendCustomerExportVo">
        SELECT c.customer_id                                                                    AS customer_id,
        f.friend_id                                                                      AS friend_id,
        f.friend_name                                                                    AS friend_name,
        f.record_date                                                                    AS record_date,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS main_channel_name,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = sub_channel_id)  AS sub_channel_name,
        f.create_time                                                                    AS create_time,
        cf.begin_time                                                                    AS update_time,
        cf.py_extend_id                                                                  AS extend_id,
        c.serve_id                                                                       AS serve_id,
        c.last_login_time                                                                AS last_login_time,
        IFNULL(SUM(b.order_money), 0)                                                AS total_recharge
        <choose>
            <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                ,IFNULL(SUM(CASE WHEN b.order_time >= #{query.rechargeBeginTime} AND
                b.order_time &lt;= #{query.rechargeEndTime} THEN order_money ELSE 0 END), 0)
                AS actual_recharge,
            </when>
            <otherwise>
                ,IFNULL(SUM(b.order_money), 0)                                       AS actual_recharge,
            </otherwise>
        </choose>
        fansUp                                                                           AS fansUp,
        fans2h                                                                           AS fans2h,
        fans5h                                                                           AS fans5h,
        fans5k                                                                           AS fans5k,
        fans5w                                                                           AS fans5w,
        u.user_id                                                                        AS extend_user_id,
        u.user_name                                                                      AS extend_user_name,
        u.nick_name                                                                      AS extend_nick_name,
        d.dept_id                                                                        AS extend_dept_id,
        d.dept_name                                                                      AS extend_dept_name,
        d.ancestors_names                                                                AS extend_ancestors_names,
        s.serve_user_id                                                                  AS serve_user_id,
        s.serve_user_name                                                                AS serve_user_name,
        s.serve_nick_name                                                                AS serve_nick_name,
        s.serve_dept_id                                                                  AS serve_dept_id,
        s.serve_dept_name                                                                AS serve_dept_name,
        s.serve_ancestors_names                                                          AS serve_ancestors_names,
        s.join_time                                                                      AS serve_join_time,
        s.lose_time                                                                      AS serve_lose_time,
        u2.user_name                                                                     AS channel_user_name,
        u2.nick_name                                                                     AS channel_nick_name,
        CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
        SELECT
        f.friend_id,
        f.friend_name,
        f.extend_id,
        f.create_time,
        f.record_date,
        f.main_channel_id,
        f.sub_channel_id,
        f.pitcher_id,
        up.py_extend_ids
        FROM crm_friend AS f
        LEFT JOIN
        (
        SELECT
        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
        user_id
        FROM yooa_system.sys_user_pd
        GROUP BY user_id
        ) up ON up.user_id = f.extend_id
        <where>
            f.extend_id IS NOT NULL
            <if test="query.createBeginTime != null ">
                AND f.create_time >= #{query.createBeginTime}
            </if>
            <if test="query.createEndTime != null ">
                AND f.create_time &lt;= #{query.createEndTime}
            </if>
            <if test="query.recordBeginTime != null ">
                AND f.record_date >= #{query.recordBeginTime}
            </if>
            <if test="query.recordEndTime != null ">
                AND f.record_date &lt;= #{query.recordEndTime}
            </if>
            <if test="query.beginAge != null ">
                AND f.age >= #{query.beginAge}
            </if>
            <if test="query.endAge != null ">
                AND f.age &lt;= #{query.endAge}
            </if>
            <if test="query.subChannelId != null ">
                AND f.sub_channel_id = #{query.subChannelId}
            </if>
            <if test="query.area != null ">
                AND f.area = #{query.area}
            </if>
            <if test="query.language != null ">
                AND f.language = #{query.language}
            </if>
            <if test="query.fansType != null ">
                AND f.fans_type = #{query.fansType}
            </if>
            <if test="query.workType != null ">
                AND f.work_type = #{query.workType}
            </if>
            <if test="query.demand != null ">
                AND f.demand = #{query.demand}
            </if>
            <if test="query.sex != null ">
                AND f.sex = #{query.sex}
            </if>
            <if test="query.mainChannelId != null ">
                AND f.main_channel_id = #{query.mainChannelId}
            </if>
            <if test="query.channelUserId != null ">
                AND f.pitcher_id = #{query.channelUserId}
                AND f.pitcher_id IS NOT NULL
            </if>
            <if test="query.receiveId != null ">
                AND f.extend_id = #{query.receiveId}
            </if>
            <!--权限范围搜索-->
            <!--个人权限-->
            <if test="query.dataScope == 0 ">
                <if test="query.userType == 1 ">
                    AND f.extend_id = #{query.userId}
                </if>
                <if test="query.userType == 8 ">
                    AND f.pitcher_id IS NOT NULL
                    AND f.pitcher_id = #{query.userId}
                </if>
            </if>
            <!--下级权限-->
            <if test="query.dataScope == 1">
                <if test="query.userType == 8">
                    AND f.pitcher_id IS NOT NULL
                </if>

                <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                    AND f.extend_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                    AND f.pitcher_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

            </if>
        </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--几百粉查询条件-->
        <if test="query.type != null or !query.sizeBl">
            LEFT JOIN (
            SELECT
            IFNULL(SUM(CASE WHEN ev.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fansUp,
            IFNULL(SUM(CASE WHEN ev.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans2h,
            IFNULL(SUM(CASE WHEN ev.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans5h,
            IFNULL(SUM(CASE WHEN ev.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5k,
            IFNULL(SUM(CASE WHEN ev.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5w,
            ev.friend_id
            <if test="query.userType == 1 or query.userType == 8">
                ,ev.extend_id AS user_id
            </if>
            <if test="query.userType == 7">
                ,ev.serve_id AS user_id
            </if>
            FROM yooa_extend.extend_vermicelli AS ev
            <where>
                <if test="query.fansBeginTime != null ">
                    record_date >= #{query.fansBeginTime}
                </if>
                <if test="query.fansEndTime != null ">
                    AND record_date &lt;= #{query.fansEndTime}
                </if>
            </where>
            GROUP BY
            <if test="query.userType == 1 or query.userType == 8">
                ev.extend_id,
            </if>
            <if test="query.userType == 7">
                ev.serve_id,
            </if>
            ev.friend_id
            ) AS ev ON ev.friend_id = f.friend_id AND ev.user_id = f.extend_id
        </if>
        <!--运营查询条件-->
        <if test="query.searchType == 3 or query.status == 2">
            RIGHT JOIN (
                SELECT
                    a.anchor_name,
                    u.user_id,
                    u.user_name,
                    u.nick_name,
                    cch.handover_id,
                    cch.extend_id,
                    cch.customer_id
                FROM
                (
                    SELECT
                        handover_id,
                        py_operate_id,
                        py_extend_id,
                        py_anchor_id,
                        operate_id,
                        extend_id,
                        customer_id
                    FROM crm_customer_handover
                    WHERE
                        handover_type = 1
                        AND handover_status = 1
                ) cch
                LEFT JOIN crm_anchor a ON a.anchor_id = cch.py_anchor_id
                LEFT JOIN yooa_system.sys_user u ON u.user_id = cch.operate_id
                <where>
                    <!--搜索框查询-运营-->
                    <if test="query.searchType == 3">
                        <if test="query.searchBox">
                            AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                            OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                        </if>
                    </if>
                </where>
                GROUP BY cch.handover_id
            ) AS cja ON cja.customer_id = c.customer_id AND cja.extend_id = f.extend_id
        </if>
        LEFT JOIN (
        SELECT
        customer_id,
        IFNULL(SUM(order_money),0)    AS order_money,
        DATE(order_time)           AS order_time,
        extend_id
        FROM
        crm_customer_order
        WHERE
        order_status = 1
        GROUP BY customer_id,extend_id,DATE(order_time)
        ) b ON c.customer_id = b.customer_id AND b.extend_id = f.extend_id
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                cch.handover_id,
                cch.receive_time                                                                 AS join_time,
                cch.lose_time                                                                    AS lose_time,
                cch.customer_id                                                                  AS customer_id,
                cch.extend_id                                                                    AS extend_id,
                u.user_id                                                                        AS serve_user_id,
                u.user_name                                                                      AS serve_user_name,
                u.nick_name                                                                      AS serve_nick_name,
                d.dept_id                                                                        AS serve_dept_id,
                d.dept_name                                                                      AS serve_dept_name,
                d.ancestors_names                                                                AS serve_ancestors_names
            FROM
            (
                SELECT
                    handover_id,
                    py_serve_id,
                    py_extend_id,
                    serve_id,
                    extend_id,
                    receive_time,
                    lose_time,
                    customer_id
                FROM crm_customer_handover
                WHERE
                    handover_type = 2
                    AND handover_status = 1
                    <!--权限范围搜索-->
                    <!--个人权限-->
                    <if test="query.dataScope == 0 ">
                        <if test="query.userType == 7 ">
                            AND serve_id = #{query.userId}
                        </if>
                    </if>
                    <!--下级权限-->
                    <if test="query.dataScope == 1 ">
                        <if test="query.userType == 7 and userIds != null and userIds.size() > 0">
                            AND serve_id IN
                            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </if>
            ) cch
            LEFT JOIN yooa_system.sys_user u ON cch.serve_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
            GROUP BY cch.handover_id
        ) s ON c.customer_id = s.customer_id AND s.extend_id = f.extend_id
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 1 ">
                    AND ev.fansUp > 0
                </if>
                <if test="query.type == 2 ">
                    AND ev.fans2h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ev.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ev.fans5w > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND (extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
                OR channel_nick_name LIKE concat('%', #{query.searchBox}, '%'))
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <select id="listFreeExport" resultType="com.yooa.crm.api.domain.vo.FreeExportVo">
        SELECT
            f.friend_id                                                                      AS friendId,
            f.friend_name                                                                    AS friendName,
            f.extend_id                                                                AS friendReceiveId,
            u.nick_name                                                                      AS friendReceiveNickName,
            SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(dept.ancestors_names,'-',dept.dept_name), '-', -3), '-', -3) AS friendReceiveDeptName,
            f.record_date                                                                    AS recordDate,
            f.create_time                                                                    AS createTime,
            ch.channel_name                                                                  AS mainChannelName,
            ch1.channel_name                                                                 AS subChannelName,
            f.pitcher_id                                                                     AS channelUserId,
            u1.nick_name                                                                     AS channelUserNickName,
            f.age                                                                            AS age,
            f.contact_mode                                                                   AS contactMode,
            f.contact_phone                                                                  AS contactPhone,
            f.income_level                                                                   AS incomeLevel,
            f.projected_consumption                                                          AS projectedConsumption,
            f.remark                                                                         AS remark,
            f.receive_number                                                                 AS receiveNumber,
            f.lose_time                                                                      AS loseTime,
            f.create_by                                                                      AS createId,
            u2.nick_name                                                                     AS createNickName,
            d.dict_label                                                                     AS sex,
            d1.dict_label                                                                    AS language,
            d2.dict_label                                                                    AS fansType,
            d3.dict_label                                                                    AS area,
            d4.dict_label                                                                    AS workType,
            d5.dict_label                                                                    AS matrimony,
            d6.dict_label                                                                    AS demand,
            GROUP_CONCAT(CASE WHEN cf.`status` = 0 THEN cf.customer_id ELSE NULL END)        AS bindCustomerId,
            GROUP_CONCAT(CASE WHEN cf.`status` = 0 THEN cf.customer_name ELSE NULL END)      AS bindCustomerName,
            GROUP_CONCAT(CASE WHEN cf.`status` = 1 THEN cf.customer_id ELSE NULL END)        AS unbindCustomerId,
            GROUP_CONCAT(CASE WHEN cf.`status` = 1 THEN cf.customer_name ELSE NULL END)      AS unbindCustomerName
        FROM (
            SELECT
                f.*
            FROM crm_friend f
            <where>
                <if test="query.updateBeginTime != null ">
                    AND f.update_time >= #{query.updateBeginTime}
                </if>
                <if test="query.updateEndTime != null ">
                    AND f.update_time &lt;= #{query.updateEndTime}
                </if>
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND f.record_date >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND f.record_date &lt;= #{query.recordEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.subChannelId != null ">
                    AND f.sub_channel_id = #{query.subChannelId}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.mainChannelId != null ">
                    AND f.main_channel_id = #{query.mainChannelId}
                </if>
                <if test="query.channelUserId != null ">
                    AND f.pitcher_id = #{query.channelUserId}
                    AND f.pitcher_id IS NOT NULL
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
            </where>
        ) f
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept dept ON u.dept_id = dept.dept_id
        LEFT JOIN yooa_system.sys_user u1 ON f.pitcher_id = u1.user_id
        LEFT JOIN yooa_system.sys_user u2 ON f.create_by = u2.user_id
        LEFT JOIN crm_friend_channel ch ON ch.channel_id = main_channel_id
        LEFT JOIN crm_friend_channel ch1 ON ch1.channel_id = sub_channel_id
        LEFT JOIN yooa_system.sys_dict_data d ON d.dict_type = 'common_sex_type' AND d.dict_value = f.sex
        LEFT JOIN yooa_system.sys_dict_data d1 ON d1.dict_type = 'sys_friend_language_type' AND d1.dict_value = f.language
        LEFT JOIN yooa_system.sys_dict_data d2 ON d2.dict_type = 'crm_friend_fans_type' AND d2.dict_value = f.fans_type
        LEFT JOIN yooa_system.sys_dict_data d3 ON d3.dict_type = 'crm_friend_area_type' AND d3.dict_value = f.area
        LEFT JOIN yooa_system.sys_dict_data d4 ON d4.dict_type = 'crm_friend_work_type' AND d4.dict_value = f.work_type
        LEFT JOIN yooa_system.sys_dict_data d5 ON d5.dict_type = 'common_marital_status' AND d5.dict_value = f.matrimony
        LEFT JOIN yooa_system.sys_dict_data d6 ON d6.dict_type = 'crm_friend_emotion_type' AND d6.dict_value = f.demand
        LEFT JOIN
            (
                SELECT
                    cf.`status`,
                    cf.customer_id,
                    cf.friend_id,
                    c.customer_name
                FROM crm_customer_friend cf
                LEFT JOIN crm_customer AS c ON c.customer_id = cf.customer_id
            ) AS cf ON f.friend_id = cf.friend_id
        GROUP BY f.friend_id
    </select>

    <select id="extendJoinList" resultType="com.yooa.crm.api.domain.vo.ExtendVo">
        SELECT
            u.nick_name                                                         AS nickName,
            d.dept_name                                                         AS deptName,
            d.ancestors_names                                                   AS ancestorsNames,
            cf.customer_id                                                      AS customerId,
            cf.begin_time                                                       AS receiveTime,
            cf.end_time                                                         AS loseTime
        FROM (
             SELECT
                 id,
                 customer_id,
                 py_extend_id,
                 begin_time,
                 end_time
             FROM crm_customer_friend
             WHERE
                friend_id = #{friendId}
             ) cf
        LEFT JOIN yooa_system.sys_user_pd up ON cf.py_extend_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = d.dept_id
        GROUP BY cf.id
    </select>

    <select id="vipJoinList" resultType="com.yooa.crm.api.domain.vo.ServeVo">
        SELECT
            u.nick_name                                                         AS nickName,
            d.dept_name                                                         AS deptName,
            d.ancestors_names                                                   AS ancestorsNames,
            cf.customer_id                                                      AS customerId,
            cf.begin_time                                                       AS receiveTime,
            cf.end_time                                                         AS loseTime
        FROM (
                 SELECT
                     customer_id,
                     py_extend_id,
                     begin_time,
                     end_time
                 FROM crm_customer_friend
                 WHERE
                     friend_id = #{friendId}
             ) cf
        LEFT JOIN crm_customer_join_serve s ON s.customer_id = cf.customer_id AND s.extend_id = cf.py_extend_id
        LEFT JOIN yooa_system.sys_user_pd up ON s.serve_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        WHERE u.user_id IS NOT NULL
        GROUP BY s.id
    </select>

    <select id="operateJoinList" resultType="com.yooa.crm.api.domain.vo.OperatorVo">
        SELECT
            u.nick_name                                                         AS nickName,
            u.user_id,
            d.dept_id,
            d.dept_name                                                         AS deptName,
            d.ancestors_names                                                   AS ancestorsNames,
            cf.customer_id                                                      AS customerId,
            cf.begin_time                                                       AS receiveTime,
            cf.end_time                                                         AS loseTime
        FROM (
                 SELECT
                     customer_id,
                     py_extend_id,
                     begin_time,
                     end_time
                 FROM crm_customer_friend
                 WHERE
                     friend_id = #{friendId}
             ) cf
        LEFT JOIN crm_customer_join_anchor cja ON cja.customer_id = cf.customer_id AND cja.extend_id = cf.py_extend_id
        LEFT JOIN yooa_system.sys_user_pd up ON cja.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        WHERE u.user_id IS NOT NULL
        GROUP BY cja.id
    </select>

    <select id="selectOperateFriendCustomerList" resultMap="FriendCustomerOperateVo">
        SELECT
            c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.create_time                                                                    AS create_time,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            cf.end_time                                                                      AS end_time,
            <if test="(query.beginMoney != null or query.endMoney != null) and query.beginTotalMoney == null and query.endTotalMoney == null">
                IFNULL(SUM(b.total_amount), 0)                                               AS actual_recharge,
            </if>
            <if test="query.beginTotalMoney != null or query.endTotalMoney != null">
                IFNULL(SUM(b.total_amount), 0)                                               AS total_recharge,
                <choose>
                    <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                        IFNULL(SUM(CASE WHEN b.add_time >= #{query.rechargeBeginTime} AND
                        b.add_time &lt;= #{query.rechargeEndTime} THEN total_amount ELSE 0 END), 0)
                                                                                             AS actual_recharge,
                    </when>
                    <otherwise>
                        IFNULL(SUM(b.total_amount), 0)                                       AS actual_recharge,
                    </otherwise>
                </choose>
            </if>
            <if test="query.type != null ">
                fansUpReward                                                             AS fansUpReward,
                fansUpRecharge                                                           AS fansUpRecharge,
                fans1h                                                                   AS fans1h,
                fans2h                                                                   AS fans2h,
                fans5h                                                                   AS fans5h,
                fans5k                                                                   AS fans5k,
                fans5w                                                                   AS fans5w,
                fans10w                                                                  AS fans10w,
            </if>
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            s.serve_user_id                                                                  AS serve_user_id,
            s.serve_user_name                                                                AS serve_user_name,
            s.serve_nick_name                                                                AS serve_nick_name,
            s.join_time                                                                      AS serve_join_time,
            s.lose_time                                                                      AS serve_lose_time,
            cja.receive_time                                                                 AS receive_time,
            cja.type                                                                         AS status,
            cja.py_operate_id                                                                AS py_operate_id,
            cja.user_id                                                                      AS operateId,
            cja.nick_name                                                                    AS operateNickName,
            cja.dept_name                                                                    AS operateDeptName,
            cja.ancestors_names                                                              AS operateAncestorsNames,
            cja.anchor_name                                                                  AS operateAnchorName,
            cja.anchor_id                                                                    AS operateAnchorId,
            cja.language                                                                     AS anchorLanguage,
            cja.sex                                                                          AS anchorSex,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
                (
                    SELECT
                        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                        user_id
                    FROM yooa_system.sys_user_pd
                    GROUP BY user_id
                ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--运营查询条件-->
        RIGHT JOIN (
            SELECT
                a.anchor_id,
                a.anchor_name,
                u.user_id,
                u.user_name,
                u.nick_name,
                d.dept_name,
                d.ancestors_names,
                cja.id,
                cja.type,
                cja.operate_id AS py_operate_id,
                cja.extend_id,
                cja.customer_id,
                cja.join_time AS receive_time,
                ai.language,
                ai.sex
            FROM
                crm_customer_join_anchor cja
            LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
            LEFT JOIN crm_anchor_account_mapping am ON am.account_id = cja.anchor_id
            LEFT JOIN crm_anchor_info ai ON ai.anchor_id = am.anchor_id
            LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
            LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                cja.status = 1
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                        AND u.user_id = #{query.userId}
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                        AND u.user_id IS NOT NULL

                    <if test="userIds != null and userIds.size() > 0">
                        AND u.user_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
                <!--部门查询条件-->
                <if test="query.deptId != null ">
                    AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
                </if>
                <if test="query.status != null">
                    AND cja.type = #{query.status}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND cja.receive_time >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND cja.receive_time &lt;= #{query.recordEndTime}
                </if>
                <!--主播信息查询-->
                <if test="query.anchorLanguage != null ">
                    AND ai.language = #{query.anchorLanguage}
                </if>
                <if test="query.anchorSex != null ">
                    AND ai.sex = #{query.anchorSex}
                </if>
            </where>
            GROUP BY cja.customer_id,cja.extend_id
        ) AS cja ON cja.customer_id = c.customer_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids ) AND cf.begin_time &lt;= cja.receive_time AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        <!--几百粉查询条件-->
        <if test="query.type != null ">
            LEFT JOIN (
                SELECT
                    IFNULL(SUM(CASE WHEN ov.fans_type = 6 THEN 1 ELSE 0 END), 0) AS fansUpReward,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 7 THEN 1 ELSE 0 END), 0) AS fansUpRecharge,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans1h,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans2h,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5h,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5k,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 4 THEN 1 ELSE 0 END), 0) AS fans5w,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fans10w,
                    ov.operate_id                                                AS user_id,
                    ov.friend_id
                FROM yooa_extend.operate_vermicelli AS ov
                <where>
                    <if test="query.fansBeginTime != null ">
                        record_date >= #{query.fansBeginTime}
                    </if>
                    <if test="query.fansEndTime != null ">
                        AND record_date &lt;= #{query.fansEndTime}
                    </if>
                </where>
                GROUP BY
                    ov.operate_id,ov.friend_id
            ) AS ov ON ov.friend_id = f.friend_id AND cja.user_id = ov.user_id
        </if>
        <if test="(query.beginMoney != null or query.endMoney != null) and query.beginTotalMoney == null and query.endTotalMoney == null">
            LEFT JOIN (
                SELECT
                    customer_id,
                    anchor_id,
                    IFNULL(SUM(total_amount),0)    AS total_amount,
                    operate_id                     AS py_operate_id
                FROM
                    crm_customer_reward
                <where>
                    <if test="query.rechargeBeginTime != null">
                        AND add_time >= #{query.rechargeBeginTime}
                    </if>
                    <if test="query.rechargeEndTime != null">
                        AND add_time &lt;= #{query.rechargeEndTime}
                    </if>
                </where>
                GROUP BY customer_id,anchor_id,operate_id
            ) b ON c.customer_id = b.customer_id AND b.py_operate_id = cja.py_operate_id AND cja.anchor_id = b.anchor_id
        </if>
        <if test="query.beginTotalMoney != null or query.endTotalMoney != null">
            LEFT JOIN (
                SELECT
                    customer_id,
                    anchor_id,
                    IFNULL(SUM(total_amount),0)    AS total_amount,
                    DATE(add_time)                 AS add_time,
                    operate_id                     AS py_operate_id
                FROM
                    crm_customer_reward
                GROUP BY customer_id,anchor_id,operate_id,DATE(add_time)
            ) b ON c.customer_id = b.customer_id AND b.py_operate_id = cja.py_operate_id AND cja.anchor_id = b.anchor_id
        </if>
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                s.*,
                u1.user_id                                                                       AS serve_user_id,
                u1.user_name                                                                     AS serve_user_name,
                u1.nick_name                                                                     AS serve_nick_name
            FROM
            (
                SELECT s.*
                FROM crm_customer_join_serve AS s
                WHERE
                    s.status = 1
                    AND s.id = (
                        SELECT MAX(id)
                        FROM crm_customer_join_serve
                        WHERE customer_id = s.customer_id AND extend_id = s.extend_id AND status = 1
                    )
                    <!--权限范围搜索-->
                    <!--个人权限-->
                    <if test="query.dataScope == 0 ">
                        AND s.serve_id IN (SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id = #{query.userId})
                    </if>
                    <!--下级权限-->
                    <if test="query.dataScope == 1 ">
                        <if test="userIds != null and userIds.size() > 0">
                            AND s.serve_id IN
                            (
                            SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id IN
                            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            )
                        </if>
                    </if>
            ) s
            LEFT JOIN yooa_system.sys_user_pd up1 ON s.serve_id = up1.pd_user_id
            LEFT JOIN yooa_system.sys_user u1 ON up1.user_id = u1.user_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 0 ">
                    AND ov.fans1h > 0
                </if>
                <if test="query.type == 1 ">
                    AND ov.fans2h > 0
                </if>
                <if test="query.type == 2 ">
                    AND ov.fans5h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ov.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ov.fans5w > 0
                </if>
                <if test="query.type == 5 ">
                    AND ov.fans10w > 0
                </if>
                <if test="query.type == 6 ">
                    AND ov.fansUpReward > 0
                </if>
                <if test="query.type == 7 ">
                    AND ov.fansUpRecharge > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <select id="selectOperateFriendCustomerNewList" resultMap="FriendCustomerOperateVo">
        SELECT
            c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.create_time                                                                    AS create_time,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            cf.end_time                                                                      AS end_time,
            <if test="query.beginMoney != null or query.endMoney != null and query.beginTotalMoney == null and query.endTotalMoney == null">
                IFNULL(SUM(b.total_amount), 0)                                               AS actual_recharge,
            </if>
            <if test="query.beginTotalMoney != null or query.endTotalMoney != null">
                IFNULL(SUM(b.total_amount), 0)                                               AS total_recharge,
                <choose>
                    <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                        IFNULL(SUM(CASE WHEN b.add_time >= #{query.rechargeBeginTime} AND
                        b.add_time &lt;= #{query.rechargeEndTime} THEN total_amount ELSE 0 END), 0)
                                                                                             AS actual_recharge,
                    </when>
                    <otherwise>
                        IFNULL(SUM(b.total_amount), 0)                                       AS actual_recharge,
                    </otherwise>
                </choose>
            </if>
            <if test="query.type != null ">
                fansUpReward                                                             AS fansUpReward,
                fansUpRecharge                                                           AS fansUpRecharge,
                fans1h                                                                   AS fans1h,
                fans2h                                                                   AS fans2h,
                fans5h                                                                   AS fans5h,
                fans5k                                                                   AS fans5k,
                fans5w                                                                   AS fans5w,
                fans10w                                                                  AS fans10w,
            </if>
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            s.serve_user_id                                                                  AS serve_user_id,
            s.serve_user_name                                                                AS serve_user_name,
            s.serve_nick_name                                                                AS serve_nick_name,
            s.join_time                                                                      AS serve_join_time,
            s.lose_time                                                                      AS serve_lose_time,
            cja.receive_time                                                                 AS receive_time,
            cja.type                                                                         AS status,
            cja.py_operate_id                                                                AS py_operate_id,
            cja.user_id                                                                      AS operateId,
            cja.nick_name                                                                    AS operateNickName,
            cja.dept_name                                                                    AS operateDeptName,
            cja.ancestors_names                                                              AS operateAncestorsNames,
            cja.anchor_name                                                                  AS operateAnchorName,
            cja.anchor_id                                                                    AS operateAnchorId,
            cja.language                                                                     AS anchorLanguage,
            cja.sex                                                                          AS anchorSex,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
                (
                    SELECT
                        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                        user_id
                    FROM yooa_system.sys_user_pd
                    GROUP BY user_id
                ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--运营查询条件-->
        RIGHT JOIN (
            SELECT
                a.anchor_id,
                a.anchor_name,
                u.user_id,
                u.user_name,
                u.nick_name,
                d.dept_name,
                d.ancestors_names,
                cch.handover_id,
                cch.handover_num AS type,
                cch.py_operate_id,
                cch.extend_id,
                cch.customer_id,
                cch.receive_time,
                ai.language,
                ai.sex
            FROM
                (
                    SELECT
                        handover_id,
                        py_operate_id,
                        py_extend_id,
                        py_anchor_id,
                        operate_id,
                        extend_id,
                        customer_id,
                        receive_time,
                        handover_num
                    FROM crm_customer_handover
                    WHERE
                        handover_type = 1
                        AND handover_status = 1
                )   cch
            LEFT JOIN crm_anchor a ON a.anchor_id = cch.py_anchor_id
            LEFT JOIN crm_anchor_account_mapping am ON am.account_id = cch.py_anchor_id
            LEFT JOIN crm_anchor_info ai ON ai.anchor_id = am.anchor_id
            LEFT JOIN yooa_system.sys_user u ON cch.operate_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                        AND u.user_id = #{query.userId}
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                        AND u.user_id IS NOT NULL

                    <if test="userIds != null and userIds.size() > 0">
                        AND u.user_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
                <!--部门查询条件-->
                <if test="query.deptId != null ">
                    AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
                </if>
                <if test="query.status != null">
                    AND cch.handover_num = #{query.status}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND cch.receive_time >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND cch.receive_time &lt;= #{query.recordEndTime}
                </if>
                <!--主播信息查询-->
                <if test="query.anchorLanguage != null ">
                    AND ai.language = #{query.anchorLanguage}
                </if>
                <if test="query.anchorSex != null ">
                    AND ai.sex = #{query.anchorSex}
                </if>
            </where>
            GROUP BY cch.handover_id
        ) AS cja ON cja.customer_id = c.customer_id AND cja.extend_id = f.extend_id AND cf.begin_time &lt;= cja.receive_time AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        <!--几百粉查询条件-->
        <if test="query.type != null ">
            LEFT JOIN (
                SELECT
                    IFNULL(SUM(CASE WHEN ov.fans_type = 6 THEN 1 ELSE 0 END), 0) AS fansUpReward,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 7 THEN 1 ELSE 0 END), 0) AS fansUpRecharge,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans1h,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans2h,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5h,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5k,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 4 THEN 1 ELSE 0 END), 0) AS fans5w,
                    IFNULL(SUM(CASE WHEN ov.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fans10w,
                    ov.operate_id                                                AS user_id,
                    ov.friend_id
                FROM yooa_extend.operate_vermicelli AS ov
                <where>
                    <if test="query.fansBeginTime != null ">
                        record_date >= #{query.fansBeginTime}
                    </if>
                    <if test="query.fansEndTime != null ">
                        AND record_date &lt;= #{query.fansEndTime}
                    </if>
                </where>
                GROUP BY
                    ov.operate_id,ov.friend_id
            ) AS ov ON ov.friend_id = f.friend_id AND cja.user_id = ov.user_id
        </if>
        <if test="query.beginMoney != null or query.endMoney != null and query.beginTotalMoney == null and query.endTotalMoney == null">
            LEFT JOIN (
                SELECT
                    customer_id,
                    anchor_id,
                    IFNULL(SUM(total_amount),0)    AS total_amount,
                    operate_id                     AS py_operate_id
                FROM
                    crm_customer_reward
                <where>
                    <if test="query.rechargeBeginTime != null">
                        AND add_time >= #{query.rechargeBeginTime}
                    </if>
                    <if test="query.rechargeEndTime != null">
                        AND add_time &lt;= #{query.rechargeEndTime}
                    </if>
                </where>
                GROUP BY customer_id,anchor_id,operate_id
            ) b ON c.customer_id = b.customer_id AND b.py_operate_id = cja.py_operate_id AND cja.anchor_id = b.anchor_id
        </if>
        <if test="query.beginTotalMoney != null or query.endTotalMoney != null">
            LEFT JOIN (
                SELECT
                    customer_id,
                    anchor_id,
                    IFNULL(SUM(total_amount),0)    AS total_amount,
                    DATE(add_time)                 AS add_time,
                    operate_id                     AS py_operate_id
                FROM
                    crm_customer_reward
                GROUP BY customer_id,anchor_id,operate_id,DATE(add_time)
            ) b ON c.customer_id = b.customer_id AND b.py_operate_id = cja.py_operate_id AND cja.anchor_id = b.anchor_id
        </if>
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                cch.handover_id,
                cch.receive_time                                                                 AS join_time,
                cch.lose_time                                                                    AS lose_time,
                cch.customer_id                                                                  AS customer_id,
                cch.extend_id                                                                    AS extend_id,
                u.user_id                                                                        AS serve_user_id,
                u.user_name                                                                      AS serve_user_name,
                u.nick_name                                                                      AS serve_nick_name
            FROM
            (
                SELECT
                    handover_id,
                    py_serve_id,
                    py_extend_id,
                    serve_id,
                    extend_id,
                    receive_time,
                    lose_time,
                    customer_id
                FROM crm_customer_handover
                WHERE
                    handover_type = 2
                    AND handover_status = 1
                    <!--权限范围搜索-->
                    <!--个人权限-->
                    <if test="query.dataScope == 0 ">
                        <if test="query.userType == 7 ">
                            AND serve_id = #{query.userId}
                        </if>
                    </if>
                    <!--下级权限-->
                    <if test="query.dataScope == 1 ">
                        <if test="userIds != null and userIds.size() > 0">
                            AND serve_id IN
                            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </if>
            ) cch
            LEFT JOIN yooa_system.sys_user u ON cch.serve_id = u.user_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
            GROUP BY cch.handover_id
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 0 ">
                    AND ov.fans1h > 0
                </if>
                <if test="query.type == 1 ">
                    AND ov.fans2h > 0
                </if>
                <if test="query.type == 2 ">
                    AND ov.fans5h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ov.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ov.fans5w > 0
                </if>
                <if test="query.type == 5 ">
                    AND ov.fans10w > 0
                </if>
                <if test="query.type == 6 ">
                    AND ov.fansUpReward > 0
                </if>
                <if test="query.type == 7 ">
                    AND ov.fansUpRecharge > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <select id="exportOperateFriendCustomerList" resultMap="FriendCustomerOperateVo">
        SELECT
            c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.create_time                                                                    AS create_time,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            IFNULL(SUM(b.total_amount), 0)                                                   AS total_recharge,
            <choose>
                <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                    IFNULL(SUM(CASE WHEN b.add_time >= #{query.rechargeBeginTime} AND
                    b.add_time &lt;= #{query.rechargeEndTime} THEN b.total_amount ELSE 0 END), 0)
                                                                                             AS actual_recharge,
                </when>
                <otherwise>
                    ,IFNULL(SUM(b.total_amount), 0)                                          AS actual_recharge,
                </otherwise>
            </choose>
            fansUpReward                                                             AS fansUpReward,
            fansUpRecharge                                                           AS fansUpRecharge,
            fans1h                                                                   AS fans1h,
            fans2h                                                                   AS fans2h,
            fans5h                                                                   AS fans5h,
            fans5k                                                                   AS fans5k,
            fans5w                                                                   AS fans5w,
            fans10w                                                                  AS fans10w,
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            s.serve_user_id                                                                  AS serve_user_id,
            s.serve_user_name                                                                AS serve_user_name,
            s.serve_nick_name                                                                AS serve_nick_name,
            s.join_time                                                                      AS serve_join_time,
            s.lose_time                                                                      AS serve_lose_time,
            cja.receive_time                                                                 AS record_time,
            cja.type                                                                         AS status,
            cja.py_operate_id                                                                AS py_operate_id,
            cja.user_id                                                                      AS operateId,
            cja.nick_name                                                                    AS operateNickName,
            cja.dept_name                                                                    AS operateDeptName,
            cja.ancestors_names                                                              AS operateAncestorsNames,
            cja.anchor_name                                                                  AS operateAnchorName,
            cja.anchor_id                                                                    AS operateAnchorId,
            cja.language                                                                     AS anchorLanguage,
            cja.sex                                                                          AS anchorSex,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
                (
                    SELECT
                        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                        user_id
                    FROM yooa_system.sys_user_pd
                    GROUP BY user_id
                ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND f.record_date >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND f.record_date &lt;= #{query.recordEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--运营查询条件-->
        RIGHT JOIN (
            SELECT
                a.anchor_id,
                a.anchor_name,
                u.user_id,
                u.user_name,
                u.nick_name,
                d.dept_name,
                d.ancestors_names,
                cja.id,
                cja.type,
                cja.operate_id AS py_operate_id,
                cja.extend_id,
                cja.customer_id,
                cja.receive_time,
                ai.language,
                ai.sex
            FROM
                crm_customer_join_anchor cja
            LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
            LEFT JOIN crm_anchor_account_mapping am ON am.account_id = cja.anchor_id
            LEFT JOIN crm_anchor_info ai ON ai.anchor_id = am.anchor_id
            LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
            LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                cja.status = 1
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                        AND u.user_id = #{query.userId}
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                        AND u.user_id IS NOT NULL

                    <if test="userIds != null and userIds.size() > 0">
                        AND u.user_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
                <!--部门查询条件-->
                <if test="query.deptId != null ">
                    AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
                </if>
                <if test="query.status != null">
                    AND cja.type = #{query.status}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND cja.receive_time >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND cja.receive_time >= #{query.recordBeginTime}
                </if>
                <!--主播信息查询-->
                <if test="query.anchorLanguage != null ">
                    AND ai.language = #{query.anchorLanguage}
                </if>
                <if test="query.anchorSex != null ">
                    AND ai.sex = #{query.anchorSex}
                </if>
            </where>
            GROUP BY cja.customer_id,cja.extend_id
        ) AS cja ON cja.customer_id = c.customer_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids ) AND cf.begin_time &lt;= cja.receive_time AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        <!--几百粉查询条件-->
        LEFT JOIN (
            SELECT
                IFNULL(SUM(CASE WHEN ov.fans_type = 6 THEN 1 ELSE 0 END), 0) AS fansUpReward,
                IFNULL(SUM(CASE WHEN ov.fans_type = 7 THEN 1 ELSE 0 END), 0) AS fansUpRecharge,
                IFNULL(SUM(CASE WHEN ov.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans1h,
                IFNULL(SUM(CASE WHEN ov.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans2h,
                IFNULL(SUM(CASE WHEN ov.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5h,
                IFNULL(SUM(CASE WHEN ov.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5k,
                IFNULL(SUM(CASE WHEN ov.fans_type = 4 THEN 1 ELSE 0 END), 0) AS fans5w,
                IFNULL(SUM(CASE WHEN ov.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fans10w,
                ov.operate_id                                                AS user_id,
                ov.friend_id
            FROM yooa_extend.operate_vermicelli AS ov
            <where>
                <if test="query.fansBeginTime != null ">
                    record_date >= #{query.fansBeginTime}
                </if>
                <if test="query.fansEndTime != null ">
                    AND record_date &lt;= #{query.fansEndTime}
                </if>
            </where>
            GROUP BY
                ov.operate_id,ov.friend_id
        ) AS ov ON ov.friend_id = f.friend_id AND cja.user_id = ov.user_id
        LEFT JOIN (
            SELECT
                customer_id,
                anchor_id,
                IFNULL(SUM(total_amount),0)    AS total_amount,
                DATE(add_time)                 AS add_time,
                operate_id                     AS py_operate_id
            FROM
                crm_customer_reward
            GROUP BY customer_id,anchor_id,operate_id,DATE(add_time)
        ) b ON c.customer_id = b.customer_id AND b.py_operate_id = cja.py_operate_id AND cja.anchor_id = b.anchor_id
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                s.*,
                u1.user_id                                                                       AS serve_user_id,
                u1.user_name                                                                     AS serve_user_name,
                u1.nick_name                                                                     AS serve_nick_name
            FROM
            (
                SELECT
                    s.*
                FROM crm_customer_join_serve AS s
                WHERE
                    s.status = 1
                    AND s.id = (
                        SELECT MAX(id)
                        FROM crm_customer_join_serve
                        WHERE customer_id = s.customer_id AND extend_id = s.extend_id AND status = 1
                    )
                    <!--权限范围搜索-->
                    <!--个人权限-->
                    <if test="query.dataScope == 0 ">
                        AND s.serve_id IN (SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id = #{query.userId})
                    </if>
                    <!--下级权限-->
                    <if test="query.dataScope == 1 ">
                        <if test="userIds != null and userIds.size() > 0">
                            AND s.serve_id IN
                            (
                            SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id IN
                            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            )
                        </if>
                    </if>
            ) s
            LEFT JOIN yooa_system.sys_user_pd up1 ON s.serve_id = up1.pd_user_id
            LEFT JOIN yooa_system.sys_user u1 ON up1.user_id = u1.user_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 0 ">
                    AND ov.fans1h > 0
                </if>
                <if test="query.type == 1 ">
                    AND ov.fans2h > 0
                </if>
                <if test="query.type == 2 ">
                    AND ov.fans5h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ov.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ov.fans5w > 0
                </if>
                <if test="query.type == 5 ">
                    AND ov.fans10w > 0
                </if>
                <if test="query.type == 6 ">
                    AND ov.fansUpReward > 0
                </if>
                <if test="query.type == 7 ">
                    AND ov.fansUpRecharge > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <select id="exportOperateFriendCustomerNewList" resultMap="FriendCustomerOperateVo">
        SELECT
            c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.create_time                                                                    AS create_time,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            IFNULL(SUM(b.total_amount), 0)                                                   AS total_recharge,
            <choose>
                <when test="query.rechargeBeginTime != null and query.rechargeEndTime != null">
                    IFNULL(SUM(CASE WHEN b.add_time >= #{query.rechargeBeginTime} AND
                    b.add_time &lt;= #{query.rechargeEndTime} THEN b.total_amount ELSE 0 END), 0)
                                                                                             AS actual_recharge,
                </when>
                <otherwise>
                    ,IFNULL(SUM(b.total_amount), 0)                                          AS actual_recharge,
                </otherwise>
            </choose>
            fansUpReward                                                             AS fansUpReward,
            fansUpRecharge                                                           AS fansUpRecharge,
            fans1h                                                                   AS fans1h,
            fans2h                                                                   AS fans2h,
            fans5h                                                                   AS fans5h,
            fans5k                                                                   AS fans5k,
            fans5w                                                                   AS fans5w,
            fans10w                                                                  AS fans10w,
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            s.serve_user_id                                                                  AS serve_user_id,
            s.serve_user_name                                                                AS serve_user_name,
            s.serve_nick_name                                                                AS serve_nick_name,
            s.join_time                                                                      AS serve_join_time,
            s.lose_time                                                                      AS serve_lose_time,
            cja.receive_time                                                                 AS record_time,
            cja.type                                                                         AS status,
            cja.py_operate_id                                                                AS py_operate_id,
            cja.user_id                                                                      AS operateId,
            cja.nick_name                                                                    AS operateNickName,
            cja.dept_name                                                                    AS operateDeptName,
            cja.ancestors_names                                                              AS operateAncestorsNames,
            cja.anchor_name                                                                  AS operateAnchorName,
            cja.anchor_id                                                                    AS operateAnchorId,
            cja.language                                                                     AS anchorLanguage,
            cja.sex                                                                          AS anchorSex,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
                (
                    SELECT
                        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                        user_id
                    FROM yooa_system.sys_user_pd
                    GROUP BY user_id
                ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND f.record_date >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND f.record_date &lt;= #{query.recordEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--运营查询条件-->
        RIGHT JOIN (
            SELECT
                a.anchor_id,
                a.anchor_name,
                u.user_id,
                u.user_name,
                u.nick_name,
                d.dept_name,
                d.ancestors_names,
                cch.handover_id,
                cch.handover_num AS type,
                cch.py_operate_id,
                cch.extend_id,
                cch.customer_id,
                cch.receive_time,
                ai.language,
                ai.sex
            FROM
            (
                SELECT
                    handover_id,
                    py_operate_id,
                    py_extend_id,
                    py_anchor_id,
                    operate_id,
                    extend_id,
                    customer_id,
                    receive_time,
                    handover_num
                FROM crm_customer_handover
                WHERE
                    handover_type = 1
                    AND handover_status = 1
            )   cch
            LEFT JOIN crm_anchor a ON a.anchor_id = cch.py_anchor_id
            LEFT JOIN crm_anchor_account_mapping am ON am.account_id = cch.py_anchor_id
            LEFT JOIN crm_anchor_info ai ON ai.anchor_id = am.anchor_id
            LEFT JOIN yooa_system.sys_user u ON cch.operate_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <where>
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    AND u.user_id = #{query.userId}
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                    AND u.user_id IS NOT NULL

                    <if test="userIds != null and userIds.size() > 0">
                        AND u.user_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
                <!--部门查询条件-->
                <if test="query.deptId != null ">
                    AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
                </if>
                <if test="query.status != null">
                    AND cch.handover_num = #{query.status}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND cch.receive_time >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND cch.receive_time &lt;= #{query.recordEndTime}
                </if>
                <!--主播信息查询-->
                <if test="query.anchorLanguage != null ">
                    AND ai.language = #{query.anchorLanguage}
                </if>
                <if test="query.anchorSex != null ">
                    AND ai.sex = #{query.anchorSex}
                </if>
            </where>
            GROUP BY cch.handover_id
        ) AS cja ON cja.customer_id = c.customer_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids ) AND cf.begin_time &lt;= cja.receive_time AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        <!--几百粉查询条件-->
        LEFT JOIN (
            SELECT
                IFNULL(SUM(CASE WHEN ov.fans_type = 6 THEN 1 ELSE 0 END), 0) AS fansUpReward,
                IFNULL(SUM(CASE WHEN ov.fans_type = 7 THEN 1 ELSE 0 END), 0) AS fansUpRecharge,
                IFNULL(SUM(CASE WHEN ov.fans_type = 0 THEN 1 ELSE 0 END), 0) AS fans1h,
                IFNULL(SUM(CASE WHEN ov.fans_type = 1 THEN 1 ELSE 0 END), 0) AS fans2h,
                IFNULL(SUM(CASE WHEN ov.fans_type = 2 THEN 1 ELSE 0 END), 0) AS fans5h,
                IFNULL(SUM(CASE WHEN ov.fans_type = 3 THEN 1 ELSE 0 END), 0) AS fans5k,
                IFNULL(SUM(CASE WHEN ov.fans_type = 4 THEN 1 ELSE 0 END), 0) AS fans5w,
                IFNULL(SUM(CASE WHEN ov.fans_type = 5 THEN 1 ELSE 0 END), 0) AS fans10w,
                ov.operate_id                                                AS user_id,
                ov.friend_id
            FROM yooa_extend.operate_vermicelli AS ov
            <where>
                <if test="query.fansBeginTime != null ">
                    record_date >= #{query.fansBeginTime}
                </if>
                <if test="query.fansEndTime != null ">
                    AND record_date &lt;= #{query.fansEndTime}
                </if>
            </where>
            GROUP BY
                ov.operate_id,ov.friend_id
        ) AS ov ON ov.friend_id = f.friend_id AND cja.user_id = ov.user_id
        LEFT JOIN (
            SELECT
                customer_id,
                anchor_id,
                IFNULL(SUM(total_amount),0)    AS total_amount,
                DATE(add_time)                 AS add_time,
                operate_id                     AS py_operate_id
            FROM
                crm_customer_reward
            GROUP BY customer_id,anchor_id,operate_id,DATE(add_time)
        ) b ON c.customer_id = b.customer_id AND b.py_operate_id = cja.py_operate_id AND cja.anchor_id = b.anchor_id
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                cch.handover_id,
                cch.receive_time                                                                 AS join_time,
                cch.lose_time                                                                    AS lose_time,
                cch.customer_id                                                                  AS customer_id,
                cch.extend_id                                                                    AS extend_id,
                u.user_id                                                                        AS serve_user_id,
                u.user_name                                                                      AS serve_user_name,
                u.nick_name                                                                      AS serve_nick_name
            FROM
            (
                SELECT
                    handover_id,
                    py_serve_id,
                    py_extend_id,
                    serve_id,
                    extend_id,
                    receive_time,
                    lose_time,
                    customer_id
                FROM crm_customer_handover
                WHERE
                    handover_type = 2
                    AND handover_status = 1
                    <!--权限范围搜索-->
                    <!--个人权限-->
                    <if test="query.dataScope == 0 ">
                        <if test="query.userType == 7 ">
                            AND serve_id = #{query.userId}
                        </if>
                    </if>
                    <!--下级权限-->
                    <if test="query.dataScope == 1 ">
                        <if test="userIds != null and userIds.size() > 0">
                            AND serve_id IN
                            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </if>
            ) cch
            LEFT JOIN yooa_system.sys_user u ON cch.serve_id = u.user_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
            GROUP BY cch.handover_id
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        <where>
            f.friend_id IS NOT NULL
            <!--更新时间筛选-->
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--几百粉查询条件-->
            <if test="query.type != null ">
                <if test="query.type == 0 ">
                    AND ov.fans1h > 0
                </if>
                <if test="query.type == 1 ">
                    AND ov.fans2h > 0
                </if>
                <if test="query.type == 2 ">
                    AND ov.fans5h > 0
                </if>
                <if test="query.type == 3 ">
                    AND ov.fans5k > 0
                </if>
                <if test="query.type == 4 ">
                    AND ov.fans5w > 0
                </if>
                <if test="query.type == 5 ">
                    AND ov.fans10w > 0
                </if>
                <if test="query.type == 6 ">
                    AND ov.fansUpReward > 0
                </if>
                <if test="query.type == 7 ">
                    AND ov.fansUpRecharge > 0
                </if>
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND s.serve_nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
            <!--搜索框查询-运营-->
            <if test="query.searchType == 3">
                <if test="query.searchBox">
                    AND (cja.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                    OR cja.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY c.customer_id,f.friend_id
        <!--搜索框查询-推广-->
        HAVING 1 = 1
        <if test="query.searchType == 1">
            <if test="query.searchBox">
                AND extend_nick_name LIKE concat('%', #{query.searchBox}, '%')
            </if>
        </if>
        <!--查询金额范围-->
        <if test="query.beginMoney != null">
            AND actual_recharge >= #{query.beginMoney}
        </if>
        <if test="query.endMoney != null">
            AND actual_recharge &lt;= #{query.endMoney}
        </if>
        <if test="query.beginTotalMoney != null">
            AND total_recharge >= #{query.beginTotalMoney}
        </if>
        <if test="query.endTotalMoney != null">
            AND total_recharge &lt;= #{query.endTotalMoney}
        </if>
        ORDER BY f.create_time DESC
    </select>

    <select id="selectRegisterAllList" resultMap="RegisterAllVo">
        SELECT c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.record_date                                                                    AS record_date,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS main_channel_name,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = sub_channel_id)  AS sub_channel_name,
            f.create_time                                                                    AS create_time,
            cf.begin_time                                                                    AS update_time,
            cf.py_extend_id                                                                  AS extend_id,
            cf.quality_time                                                                  AS quality_time,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            s.serve_user_id                                                                  AS serve_user_id,
            s.serve_user_name                                                                AS serve_user_name,
            s.serve_nick_name                                                                AS serve_nick_name,
            s.serve_dept_id                                                                  AS serve_dept_id,
            s.serve_dept_name                                                                AS serve_dept_name,
            s.serve_ancestors_names                                                          AS serve_ancestors_names,
            s.join_time                                                                      AS serve_join_time,
            s.lose_time                                                                      AS serve_lose_time,
            u2.user_name                                                                     AS channel_user_name,
            u2.nick_name                                                                     AS channel_nick_name,
            CASE WHEN cf1.id IS NOT NULL THEN '大号' ELSE '小号' END                          AS accountType,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                f.main_channel_id,
                f.sub_channel_id,
                f.pitcher_id,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
                (
                    SELECT
                        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                        user_id
                    FROM yooa_system.sys_user_pd
                    GROUP BY user_id
                 ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND f.record_date >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND f.record_date &lt;= #{query.recordEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.subChannelId != null ">
                    AND f.sub_channel_id = #{query.subChannelId}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.mainChannelId != null ">
                    AND f.main_channel_id = #{query.mainChannelId}
                </if>
                <if test="query.channelUserId != null ">
                    AND f.pitcher_id = #{query.channelUserId}
                    AND f.pitcher_id IS NOT NULL
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    <if test="query.userType == 1 ">
                        AND f.extend_id = #{query.userId}
                    </if>
                    <if test="query.userType == 8 ">
                        AND f.pitcher_id IS NOT NULL
                        AND f.pitcher_id = #{query.userId}
                    </if>
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                    <if test="query.userType == 8">
                        AND f.pitcher_id IS NOT NULL
                    </if>

                    <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                        AND f.extend_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                        AND f.pitcher_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN
        (
            SELECT
                cf.id
            FROM yooa_crm.crm_customer_friend AS cf
            LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
            GROUP BY cf.friend_id,up.user_id
        ) AS cf1 ON cf1.id = cf.id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--运营查询条件-->
        <if test="query.userType == 2 or query.searchType == 3 or query.status == 2">
        RIGHT JOIN (
            SELECT
                a.anchor_name,
                u.user_id,
                u.user_name,
                u.nick_name,
                cja.extend_id,
                cja.customer_id
            FROM crm_customer_join_anchor cja
            LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
            LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
            LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
            LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
            <where>
                cja.status = 1
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    <if test="query.userType == 2 ">
                        AND u.user_id = #{query.userId}
                    </if>
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                    <if test="query.userType == 2">
                        AND u.user_id IS NOT NULL
                    </if>

                    <if test="query.userType == 2 and userIds != null and userIds.size() > 0">
                        AND u.user_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
            </where>
            GROUP BY cja.customer_id,cja.extend_id
        ) AS cja ON cja.customer_id = c.customer_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids )
        </if>
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT
                s.*,
                u1.user_id                                                                       AS serve_user_id,
                u1.user_name                                                                     AS serve_user_name,
                u1.nick_name                                                                     AS serve_nick_name,
                d1.dept_id                                                                       AS serve_dept_id,
                d1.dept_name                                                                     AS serve_dept_name,
                d1.ancestors_names                                                               AS serve_ancestors_names
            FROM
            (
                SELECT s.*
                FROM crm_customer_join_serve AS s
                WHERE
                    s.status = 1
                AND s.id = (
                        SELECT MAX(id)
                        FROM crm_customer_join_serve
                        WHERE customer_id = s.customer_id AND extend_id = s.extend_id AND status = 1
                    )
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    <if test="query.userType == 7 ">
                        AND s.serve_id IN (SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id = #{query.userId})
                    </if>
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1 ">
                    <if test="query.userType == 7 and userIds != null and userIds.size() > 0">
                        AND s.serve_id IN
                        (
                        SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                </if>
            ) s
            LEFT JOIN yooa_system.sys_user_pd up1 ON s.serve_id = up1.pd_user_id
            LEFT JOIN yooa_system.sys_user u1 ON up1.user_id = u1.user_id
            LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
            <where>
                <!--搜索框查询-客服-->
                <if test="query.searchType == 2">
                    <if test="query.searchBox">
                        AND u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    </if>
                </if>
            </where>
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        <where>
            c.customer_id IS NOT NULL
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--优质搜索-->
            <if test="query.qualityType == 0 ">
                AND cf.quality_time IS NOT NULL
            </if>
            <if test="query.qualityType == 1 ">
                AND cf.quality_time IS NULL
            </if>
            <if test="query.qualityBeginTime != null ">
                AND cf.quality_time >= #{query.qualityBeginTime}
            </if>
            <if test="query.qualityEndTime != null ">
                AND cf.quality_time &lt;= #{query.qualityEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <if test="query.accountType == 0">
                AND cf1.id IS NOT NULL
            </if>
            <if test="query.accountType == 1">
                AND cf1.id IS NULL
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-推广-->
            <if test="query.searchType == 1">
                <if test="query.searchBox">
                    AND (u.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    OR u2.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY cf.customer_id,cf.friend_id
        <choose>
            <when test="query.qualityType == 0 ">
                ORDER BY cf.quality_time DESC
            </when>
            <otherwise>
                ORDER BY f.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="exportRegisterAllList" resultMap="RegisterAllExportVo">
        SELECT c.customer_id                                                                    AS customer_id,
        f.friend_id                                                                      AS friend_id,
        f.friend_name                                                                    AS friend_name,
        f.record_date                                                                    AS record_date,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS main_channel_name,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = sub_channel_id)  AS sub_channel_name,
        f.create_time                                                                    AS create_time,
        cf.begin_time                                                                    AS update_time,
        cf.py_extend_id                                                                  AS extend_id,
        cf.quality_time                                                                  AS quality_time,
        c.serve_id                                                                       AS serve_id,
        c.last_login_time                                                                AS last_login_time,
        u.user_id                                                                        AS extend_user_id,
        u.user_name                                                                      AS extend_user_name,
        u.nick_name                                                                      AS extend_nick_name,
        d.dept_id                                                                        AS extend_dept_id,
        d.dept_name                                                                      AS extend_dept_name,
        d.ancestors_names                                                                AS extend_ancestors_names,
        s.serve_user_id                                                                  AS serve_user_id,
        s.serve_user_name                                                                AS serve_user_name,
        s.serve_nick_name                                                                AS serve_nick_name,
        s.serve_dept_id                                                                  AS serve_dept_id,
        s.serve_dept_name                                                                AS serve_dept_name,
        s.serve_ancestors_names                                                          AS serve_ancestors_names,
        s.join_time                                                                      AS serve_join_time,
        s.lose_time                                                                      AS serve_lose_time,
        u2.user_name                                                                     AS channel_user_name,
        u2.nick_name                                                                     AS channel_nick_name,
        CASE WHEN cf1.id IS NOT NULL THEN '大号' ELSE '小号' END                          AS accountType,
        CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
        SELECT
        f.friend_id,
        f.friend_name,
        f.extend_id,
        f.create_time,
        f.record_date,
        f.main_channel_id,
        f.sub_channel_id,
        f.pitcher_id,
        up.py_extend_ids
        FROM crm_friend AS f
        LEFT JOIN
        (
        SELECT
        GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
        user_id
        FROM yooa_system.sys_user_pd
        GROUP BY user_id
        ) up ON up.user_id = f.extend_id
        <where>
            f.extend_id IS NOT NULL
            <if test="query.createBeginTime != null ">
                AND f.create_time >= #{query.createBeginTime}
            </if>
            <if test="query.createEndTime != null ">
                AND f.create_time &lt;= #{query.createEndTime}
            </if>
            <if test="query.recordBeginTime != null ">
                AND f.record_date >= #{query.recordBeginTime}
            </if>
            <if test="query.recordEndTime != null ">
                AND f.record_date &lt;= #{query.recordEndTime}
            </if>
            <if test="query.beginAge != null ">
                AND f.age >= #{query.beginAge}
            </if>
            <if test="query.endAge != null ">
                AND f.age &lt;= #{query.endAge}
            </if>
            <if test="query.subChannelId != null ">
                AND f.sub_channel_id = #{query.subChannelId}
            </if>
            <if test="query.area != null ">
                AND f.area = #{query.area}
            </if>
            <if test="query.language != null ">
                AND f.language = #{query.language}
            </if>
            <if test="query.fansType != null ">
                AND f.fans_type = #{query.fansType}
            </if>
            <if test="query.workType != null ">
                AND f.work_type = #{query.workType}
            </if>
            <if test="query.workType != null ">
                AND f.work_type = #{query.workType}
            </if>
            <if test="query.demand != null ">
                AND f.demand = #{query.demand}
            </if>
            <if test="query.sex != null ">
                AND f.sex = #{query.sex}
            </if>
            <if test="query.mainChannelId != null ">
                AND f.main_channel_id = #{query.mainChannelId}
            </if>
            <if test="query.channelUserId != null ">
                AND f.pitcher_id = #{query.channelUserId}
                AND f.pitcher_id IS NOT NULL
            </if>
            <if test="query.receiveId != null ">
                AND f.extend_id = #{query.receiveId}
            </if>
            <!--权限范围搜索-->
            <!--个人权限-->
            <if test="query.dataScope == 0 ">
                <if test="query.userType == 1 ">
                    AND f.extend_id = #{query.userId}
                </if>
                <if test="query.userType == 8 ">
                    AND f.pitcher_id IS NOT NULL
                    AND f.pitcher_id = #{query.userId}
                </if>
            </if>
            <!--下级权限-->
            <if test="query.dataScope == 1">
                <if test="query.userType == 8">
                    AND f.pitcher_id IS NOT NULL
                </if>

                <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                    AND f.extend_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                    AND f.pitcher_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

            </if>
        </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN
        (
            SELECT
                cf.id
            FROM yooa_crm.crm_customer_friend AS cf
            LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
            GROUP BY cf.friend_id,up.user_id
        ) AS cf1 ON cf1.id = cf.id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <!--运营查询条件-->
        <if test="query.userType == 2 or query.searchType == 3 or query.status == 2">
            RIGHT JOIN (
            SELECT
            a.anchor_name,
            u.user_id,
            u.user_name,
            u.nick_name,
            cja.extend_id,
            cja.customer_id
            FROM crm_customer_join_anchor cja
            LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
            LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
            LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
            LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
            <where>
                cja.status = 1
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    <if test="query.userType == 2 ">
                        AND u.user_id = #{query.userId}
                    </if>
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                    <if test="query.userType == 2">
                        AND u.user_id IS NOT NULL
                    </if>

                    <if test="query.userType == 2 and userIds != null and userIds.size() > 0">
                        AND u.user_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
                <!--搜索框查询-运营-->
                <if test="query.searchType == 3">
                    <if test="query.searchBox">
                        AND (a.anchor_name LIKE concat('%', #{query.searchBox}, '%')
                        OR u.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                    </if>
                </if>
            </where>
            GROUP BY cja.customer_id,cja.extend_id
            ) AS cja ON cja.customer_id = c.customer_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids )
        </if>
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
        SELECT
        s.*,
        u1.user_id                                                                       AS serve_user_id,
        u1.user_name                                                                     AS serve_user_name,
        u1.nick_name                                                                     AS serve_nick_name,
        d1.dept_id                                                                       AS serve_dept_id,
        d1.dept_name                                                                     AS serve_dept_name,
        d1.ancestors_names                                                               AS serve_ancestors_names
        FROM
        (
        SELECT s.*
        FROM crm_customer_join_serve AS s
        WHERE
        s.status = 1
        AND s.id = (
        SELECT MAX(id)
        FROM crm_customer_join_serve
        WHERE customer_id = s.customer_id AND extend_id = s.extend_id AND status = 1
        )
        <!--权限范围搜索-->
        <!--个人权限-->
        <if test="query.dataScope == 0 ">
            <if test="query.userType == 7 ">
                AND s.serve_id IN (SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id = #{query.userId})
            </if>
        </if>
        <!--下级权限-->
        <if test="query.dataScope == 1 ">
            <if test="query.userType == 7 and userIds != null and userIds.size() > 0">
                AND s.serve_id IN
                (
                SELECT GROUP_CONCAT(pd_user_id) FROM yooa_system.sys_user_pd pp WHERE user_id IN
                <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        ) s
        LEFT JOIN yooa_system.sys_user_pd up1 ON s.serve_id = up1.pd_user_id
        LEFT JOIN yooa_system.sys_user u1 ON up1.user_id = u1.user_id
        LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        <where>
            <!--搜索框查询-客服-->
            <if test="query.searchType == 2">
                <if test="query.searchBox">
                    AND u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                </if>
            </if>
        </where>
        ) s ON c.customer_id = s.customer_id AND FIND_IN_SET( s.extend_id, f.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        <where>
            c.customer_id IS NOT NULL
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--优质搜索-->
            <if test="query.qualityType == 0 ">
                AND cf.quality_time IS NOT NULL
            </if>
            <if test="query.qualityType == 1 ">
                AND cf.quality_time IS NULL
            </if>
            <if test="query.qualityBeginTime != null ">
                AND cf.quality_time >= #{query.qualityBeginTime}
            </if>
            <if test="query.qualityEndTime != null ">
                AND cf.quality_time &lt;= #{query.qualityEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.serveBeginTime != null ">
                AND s.join_time >= #{query.serveBeginTime}
            </if>
            <if test="query.serveEndTime != null ">
                AND s.join_time &lt;= #{query.serveEndTime}
            </if>
            <if test="query.accountType == 0">
                AND cf1.id IS NOT NULL
            </if>
            <if test="query.accountType == 1">
                AND cf1.id IS NULL
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
                <if test="query.status == 2 ">
                    AND cja.customer_id IS NOT NULL
                </if>
                <if test="query.status == 3">
                    AND s.serve_id IS NOT NULL
                </if>
            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-推广-->
            <if test="query.searchType == 1">
                <if test="query.searchBox">
                    AND (u.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    OR u2.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY cf.customer_id,cf.friend_id
        <choose>
            <when test="query.qualityType == 0 ">
                ORDER BY cf.quality_time DESC
            </when>
            <otherwise>
                ORDER BY f.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectRegisterExtendList" resultMap="RegisterExtendVo">
        SELECT c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.record_date                                                                    AS record_date,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS main_channel_name,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = sub_channel_id)  AS sub_channel_name,
            f.create_time                                                                    AS create_time,
            cf.begin_time                                                                    AS update_time,
            cf.quality_time                                                                  AS quality_time,
            c.extend_id                                                                      AS extend_id,
            c.serve_id                                                                       AS serve_id,
            c.last_login_time                                                                AS last_login_time,
            u.user_id                                                                        AS extend_user_id,
            u.user_name                                                                      AS extend_user_name,
            u.nick_name                                                                      AS extend_nick_name,
            d.dept_id                                                                        AS extend_dept_id,
            d.dept_name                                                                      AS extend_dept_name,
            d.ancestors_names                                                                AS extend_ancestors_names,
            u2.user_name                                                                     AS channel_user_name,
            u2.nick_name                                                                     AS channel_nick_name,
            CASE WHEN cf1.id IS NOT NULL THEN '大号' ELSE '小号' END                          AS accountType,
            CASE WHEN cf.`status` = 0 THEN NULL ELSE cf.end_time END                         AS lose_time
        FROM (
            SELECT
                f.friend_id,
                f.friend_name,
                f.extend_id,
                f.create_time,
                f.record_date,
                f.main_channel_id,
                f.sub_channel_id,
                f.pitcher_id,
                up.py_extend_ids
            FROM crm_friend AS f
            LEFT JOIN
            (
                SELECT
                    GROUP_CONCAT( pd_user_id ) AS py_extend_ids,
                    user_id
                FROM yooa_system.sys_user_pd
                GROUP BY user_id
            ) up ON up.user_id = f.extend_id
            <where>
                f.extend_id IS NOT NULL
                <if test="query.createBeginTime != null ">
                    AND f.create_time >= #{query.createBeginTime}
                </if>
                <if test="query.createEndTime != null ">
                    AND f.create_time &lt;= #{query.createEndTime}
                </if>
                <if test="query.recordBeginTime != null ">
                    AND f.record_date >= #{query.recordBeginTime}
                </if>
                <if test="query.recordEndTime != null ">
                    AND f.record_date &lt;= #{query.recordEndTime}
                </if>
                <if test="query.beginAge != null ">
                    AND f.age >= #{query.beginAge}
                </if>
                <if test="query.endAge != null ">
                    AND f.age &lt;= #{query.endAge}
                </if>
                <if test="query.subChannelId != null ">
                    AND f.sub_channel_id = #{query.subChannelId}
                </if>
                <if test="query.area != null ">
                    AND f.area = #{query.area}
                </if>
                <if test="query.language != null ">
                    AND f.language = #{query.language}
                </if>
                <if test="query.fansType != null ">
                    AND f.fans_type = #{query.fansType}
                </if>
                <if test="query.workType != null ">
                    AND f.work_type = #{query.workType}
                </if>
                <if test="query.demand != null ">
                    AND f.demand = #{query.demand}
                </if>
                <if test="query.sex != null ">
                    AND f.sex = #{query.sex}
                </if>
                <if test="query.mainChannelId != null ">
                    AND f.main_channel_id = #{query.mainChannelId}
                </if>
                <if test="query.channelUserId != null ">
                    AND f.pitcher_id = #{query.channelUserId}
                    AND f.pitcher_id IS NOT NULL
                </if>
                <if test="query.receiveId != null ">
                    AND f.extend_id = #{query.receiveId}
                </if>
                <!--权限范围搜索-->
                <!--个人权限-->
                <if test="query.dataScope == 0 ">
                    <if test="query.userType == 1 ">
                        AND f.extend_id = #{query.userId}
                    </if>
                    <if test="query.userType == 8 ">
                        AND f.pitcher_id IS NOT NULL
                        AND f.pitcher_id = #{query.userId}
                    </if>
                </if>
                <!--下级权限-->
                <if test="query.dataScope == 1">
                    <if test="query.userType == 8">
                        AND f.pitcher_id IS NOT NULL
                    </if>

                    <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                        AND f.extend_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                        AND f.pitcher_id IN
                        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                </if>
            </where>
        ) AS f
        LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        LEFT JOIN
        (
            SELECT
                cf.id
            FROM yooa_crm.crm_customer_friend AS cf
            LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
            GROUP BY cf.friend_id,up.user_id
        ) AS cf1 ON cf1.id = cf.id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        <where>
            c.customer_id IS NOT NULL
            <if test="query.updateBeginTime != null ">
                AND cf.begin_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND cf.begin_time &lt;= #{query.updateEndTime}
            </if>
            <!--优质搜索-->
            <if test="query.qualityType == 0 ">
                AND cf.quality_time IS NOT NULL
            </if>
            <if test="query.qualityType == 1 ">
                AND cf.quality_time IS NULL
            </if>
            <if test="query.qualityBeginTime != null ">
                AND cf.quality_time >= #{query.qualityBeginTime}
            </if>
            <if test="query.qualityEndTime != null ">
                AND cf.quality_time &lt;= #{query.qualityEndTime}
            </if>
            <!--查询绑定状态-->
            <if test="query.bindStatus == 0">
                AND cf.`status` = 0
            </if>
            <if test="query.bindStatus == 1">
                AND cf.`status` = 1
            </if>
            <if test="query.lastLoginBeginTime != null ">
                AND c.last_login_time >= #{query.lastLoginBeginTime}
            </if>
            <if test="query.lastLoginEndTime != null ">
                AND c.last_login_time &lt;= #{query.lastLoginEndTime}
            </if>
            <if test="query.accountType == 0">
                AND cf1.id IS NOT NULL
            </if>
            <if test="query.accountType == 1">
                AND cf1.id IS NULL
            </if>
            <!--状态查询条件-->
            <if test="query.status != null ">
                <if test="query.status == 0 ">
                    AND c.customer_id IS NULL
                </if>
                <if test="query.status == 1 ">
                    AND c.customer_id IS NOT NULL
                </if>
            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <!--搜索框查询-客户-->
            <if test="query.searchType == 0">
                <if test="query.searchBox">
                    AND (c.customer_id = #{query.searchBox}
                    OR f.friend_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
            <!--搜索框查询-推广-->
            <if test="query.searchType == 1">
                <if test="query.searchBox">
                    AND (u.nick_name LIKE concat('%', #{query.searchBox}, '%')
                    OR u2.nick_name LIKE concat('%', #{query.searchBox}, '%'))
                </if>
            </if>
        </where>
        GROUP BY cf.customer_id,cf.friend_id
        <choose>
            <when test="query.qualityType == 0 ">
                ORDER BY cf.quality_time DESC
            </when>
            <otherwise>
                ORDER BY f.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectFriendList" resultMap="CustomerVoMap">
        SELECT
            <include refid="FriendReceiveRecordDtoSql_f"/>
        FROM crm_friend AS f
        LEFT JOIN yooa_system.sys_user u2 ON f.extend_id = u2.user_id
        LEFT JOIN yooa_system.sys_dept d2 ON u2.dept_id = d2.dept_id
        LEFT JOIN yooa_system.sys_user u4 ON f.pitcher_id = u4.user_id
        LEFT JOIN crm_friend_channel fc1 ON fc1.channel_id = f.main_channel_id
        LEFT JOIN crm_friend_channel fc2 ON fc2.channel_id = f.sub_channel_id
        LEFT JOIN crm_friend_area fa1 ON fa1.id = f.main_area_id
        LEFT JOIN crm_friend_area fa2 ON fa2.id = f.sub_area_id
        <where>
            f.extend_id IS NOT NULL
            AND (f.type = 1 OR f.type is null
            OR (f.type = 2 AND f.polling_type = 1 AND EXISTS (
            SELECT 1
            FROM crm_customer_friend pollingcf
            WHERE
            pollingcf.friend_id = f.friend_id
            )))
            <if test="query.searchBox">
                AND f.friend_name LIKE concat('%', #{query.searchBox}, '%')
            </if>
            <if test="query.updateBeginTime != null ">
                AND f.update_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null ">
                AND f.update_time &lt;= #{query.updateEndTime}
            </if>
            <if test="query.createBeginTime != null ">
                AND f.create_time >= #{query.createBeginTime}
            </if>
            <if test="query.createEndTime != null ">
                AND f.create_time &lt;= #{query.createEndTime}
            </if>
            <if test="query.recordBeginTime != null ">
                AND f.record_date >= #{query.recordBeginTime}
            </if>
            <if test="query.recordEndTime != null ">
                AND f.record_date &lt;= #{query.recordEndTime}
            </if>
            <if test="query.beginAge != null ">
                AND f.age >= #{query.beginAge}
            </if>
            <if test="query.endAge != null ">
                AND f.age &lt;= #{query.endAge}
            </if>
            <if test="query.subChannelId != null ">
                AND f.sub_channel_id = #{query.subChannelId}
            </if>
            <if test="query.area != null ">
                AND f.area = #{query.area}
            </if>
            <if test="query.language != null ">
                AND f.language = #{query.language}
            </if>
            <if test="query.fansType != null ">
                AND f.fans_type = #{query.fansType}
            </if>
            <if test="query.workType != null ">
                AND f.work_type = #{query.workType}
            </if>
            <if test="query.demand != null ">
                AND f.demand = #{query.demand}
            </if>
            <if test="query.sex != null ">
                AND f.sex = #{query.sex}
            </if>
            <if test="query.mainChannelId != null ">
                AND f.main_channel_id = #{query.mainChannelId}
            </if>
            <if test="query.channelUserId != null ">
                AND f.pitcher_id = #{query.channelUserId}
                AND f.pitcher_id IS NOT NULL
            </if>
            <if test="query.receiveId != null ">
                AND f.extend_id = #{query.receiveId}
            </if>
            <!--权限范围搜索-->
            <!--个人权限-->
            <if test="query.dataScope == 0 ">
                <if test="query.userType == 1 ">
                    AND f.extend_id = #{query.userId}
                </if>
                <if test="query.userType == 8 ">
                    AND f.pitcher_id IS NOT NULL
                    AND f.pitcher_id = #{query.userId}
                </if>
            </if>
            <!--下级权限-->
            <if test="query.dataScope == 1">
                <if test="query.userType == 8">
                    AND f.pitcher_id IS NOT NULL
                </if>

                <if test="query.userType == 1 and userIds != null and userIds.size() > 0">
                    AND f.extend_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.userType == 8 and userIds != null and userIds.size() > 0">
                    AND f.pitcher_id IN
                    <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

            </if>
            <!--部门查询条件-->
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d2.ancestors) OR d2.dept_id = #{query.deptId})
            </if>
        </where>
        ORDER BY f.create_time DESC
    </select>

    <select id="selMycustomerIds" resultType="java.lang.Long">
        SELECT DISTINCT c.customer_id
        FROM crm_friend AS f
        LEFT JOIN crm_customer_friend AS c ON f.friend_id = c.friend_id
        <where>
            <choose>
                <when test="tfType == null or tfType == 0">
                    <if test="userid != null and userid != 0">
                        f.extend_id = #{userid}
                    </if>
                </when>
                <otherwise>
                    <if test="userid != null and userid != 0">
                        f.pitcher_id = #{userid}
                    </if>
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="GroupPeopleNumber" resultType="com.yooa.crm.api.domain.vo.GroupPeopleNumberVo">
        SELECT
            d.dict_label AS name,
            COUNT(f.friend_id) AS number
        FROM yooa_system.sys_dict_data AS d
        LEFT JOIN (
            SELECT f.*
            FROM crm_friend AS f
            RIGHT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
            <where>
                <if test="dto.beginTime != null">
                    f.record_date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND f.record_date &lt;= #{dto.endTime}
                </if>
                <if test="dto.district != null">
                    AND f.area = #{dto.district}
                </if>
            </where>
            GROUP BY f.friend_id
        ) AS f ON d.dict_value = #{groupStr1}
        WHERE d.dict_type = #{groupStr}
        GROUP BY d.dict_value
    </select>

    <select id="districtGroupPeople" resultType="com.yooa.crm.api.domain.vo.GroupPeopleNumberVo">
        SELECT
            f.record_date AS name,
            COUNT(f.friend_id) AS number
        FROM yooa_system.sys_dict_data AS d
        LEFT JOIN (
        SELECT f.* FROM crm_friend AS f
        RIGHT JOIN crm_customer_friend AS cf ON cf.friend_id = f.friend_id
        <where>
            <if test="dto.beginTime != null">
                f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
            <if test="dto.district != null">
                AND f.area = #{dto.district}
            </if>
        </where>
        GROUP BY f.friend_id
        ) AS f ON d.dict_value = f.area
        WHERE d.dict_type = 'crm_friend_area_type'
        GROUP BY f.create_time
    </select>


    <select id="SexAgePeopleNumber" resultType="com.yooa.crm.api.domain.vo.SexAgePeopleNumberVo">
        SELECT
            d.dict_label AS sex,
            COUNT(f.friend_id) AS number,
            f.age AS age
        FROM yooa_system.sys_dict_data AS d
        LEFT JOIN (
        SELECT f.* FROM crm_friend AS f
        RIGHT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
        <where>
            <if test="dto.beginTime != null">
                f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
            <if test="dto.district != null">
                AND f.area = #{dto.district}
            </if>
        </where>
        GROUP BY f.friend_id
        ) AS f ON d.dict_value = f.sex
        WHERE d.dict_type = "sys_user_sex"
        GROUP BY d.dict_value,f.age
    </select>

    <select id="customerFriend" resultType="com.yooa.crm.api.domain.vo.FriendCustomerVo">
        SELECT
            c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.record_date                                                                    AS record_date,
            c.STATUS                                                                         AS STATUS,
            f.create_time                                                                    AS create_time,
            f.remark                                                                         AS remark,
            c.update_time                                                                    AS update_time,
            c.last_login_time                                                                AS last_login_time
        FROM crm_friend f
        LEFT JOIN crm_customer_friend AS cf ON cf.friend_id = f.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <where>
            <if test="dto.beginTime != null">
                f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
            <if test="dto.userIds != null">
                AND f.extend_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.searchBox != null and dto.searchBox != ''">
                AND (c.friend_id = #{dto.searchBox}
                OR c.customer_name like concat('%', #{dto.searchBox}, '%') )
            </if>
        </where>
        ORDER BY f.create_time DESC
    </select>

    <select id="customerRegister" resultType="com.yooa.crm.api.domain.vo.FriendCustomerVo">
        SELECT
            c.customer_id                                                                    AS customer_id,
            f.friend_id                                                                      AS friend_id,
            f.friend_name                                                                    AS friend_name,
            f.record_date                                                                    AS record_date,
            c.STATUS                                                                         AS STATUS,
            f.create_time                                                                    AS create_time,
            f.remark                                                                         AS remark,
            c.update_time                                                                    AS update_time,
            c.last_login_time                                                                AS last_login_time
        FROM crm_friend f
        LEFT JOIN crm_customer_friend AS cf ON cf.friend_id = f.friend_id
        LEFT JOIN crm_customer AS c ON cf.customer_id = c.customer_id
        <where>
            <if test="dto.beginTime != null">
                f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
            <if test="dto.userIds != null">
                AND f.extend_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.searchBox != null and dto.searchBox != ''">
                AND (c.friend_id = #{dto.searchBox}
                OR c.customer_name like concat('%', #{dto.searchBox}, '%') )
            </if>
        </where>
        ORDER BY f.create_time DESC
    </select>

    <select id="friendSchedulesSelect" resultType="java.lang.Long">
        <!-- 绑定好友 充值客户30天未登录 未充值客户15天未登录 -->
        SELECT
            f.friend_id
        FROM
            crm_friend f
        INNER JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
        LEFT JOIN crm_customer c ON cf.customer_id = c.customer_id
        LEFT JOIN ( SELECT DISTINCT customer_id FROM crm_customer_order ) co ON co.customer_id = c.customer_id
        WHERE
            f.STATUS = 1
            AND f.contact_phone IS NOT NULL
            AND ((
        co.customer_id IS NOT NULL
        AND DATEDIFF( CURRENT_DATE, c.last_login_time ) > 30 )
                OR (co.customer_id IS NULL
                AND DATEDIFF( CURRENT_DATE, c.last_login_time ) > 15)
            )
        <!-- 未绑定好友 15天内没有任何更新
        UNION
        SELECT
            f.friend_id
        FROM
            crm_friend f
        LEFT JOIN crm_customer_friend cf ON f.friend_id = cf.friend_id
        WHERE
            cf.id IS NULL
            AND f.contact_phone IS NOT NULL
            AND DATEDIFF( CURRENT_DATE, f.update_time ) > 15
           -->
        <!-- 领取后15天内没有任务操作 -->
        UNION
        SELECT
            f.friend_id
        FROM
            crm_friend f
        where status = 0
            AND f.contact_phone IS NOT NULL
            AND DATEDIFF( CURRENT_DATE, f.update_time ) > 15
    </select>

    <update id="friendSchedulesUpdate">
        UPDATE crm_friend
        SET status = 2, lose_time = NOW()
        WHERE
        friend_id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <resultMap id="FriendVoMap" type="com.yooa.crm.api.domain.vo.FriendTaskVo">
        <id property="friendId" column="friend_id" jdbcType="INTEGER"/>
        <collection property="list" ofType="com.yooa.crm.api.domain.CrmCustomerFriend">
            <id property="customerId" column="customer_id" jdbcType="BIGINT"/>
            <id property="friendId" column="friend_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="DATE"/>
        </collection>
    </resultMap>

    <update id="updateStatusByCustomerId">
        UPDATE crm_friend f
            LEFT JOIN crm_customer_friend cf ON cf.friend_id = f.friend_id
        SET f.`status` = 1
        WHERE
            f.`status` = 2
          AND cf.customer_id = #{customerId}
          AND f.extend_id = #{extendId}
          AND f.lose_time IS NULL
    </update>

    <select id="getFriend" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT f.friend_id) AS friend,
            user_id AS id,
            nick_name AS name
        FROM (<include refid="getDeptJuniorIds"/>) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN (SELECT * FROM crm_friend AS f
        <where>
            <if test="dto.beginTime != null">
                AND f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
        </where>
        ) AS f ON u.user_id = f.extend_id
        <where>
            u.user_id IS NOT NULL
            <if test="dto.userIds != null and dto.userIds.size() > 0">
                AND u.user_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY user_id
    </select>

    <select id="getFriendDept" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT r.id) AS friend,
            d.dept_id AS id,
            d.dept_name AS name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (SELECT * FROM crm_friend AS f
        <where>
            <if test="dto.beginTime != null">
                AND f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
        </where>
        ) AS f ON FIND_IN_SET(f.receive_dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="getFriendDeptTimeGroup" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT f.friend_id) AS friend
            <include refid="selGroupTime"/>
        FROM (
            <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (SELECT * FROM crm_friend AS f
        <where>
            <if test="dto.beginTime != null">
                f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
        </where>
        ) AS f ON FIND_IN_SET(f.receive_dept_id,d.juniorIds)
        <include refid="groupTime"/>
    </select>

    <select id="getFriendUserTimeGroup" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT f.friend_id) AS friend
            <include refid="selGroupTime"/>
        FROM yooa_system.sys_user AS u
        LEFT JOIN (SELECT * FROM crm_friend AS f
        <where>
            <if test="dto.userIds != null and dto.userIds.size() > 0">
                f.extend_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.beginTime != null">
                AND f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
        </where>
        ) AS f ON u.user_id = f.extend_id
        <include refid="groupTime"/>
    </select>

    <select id="getRegister" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT r.customer_id) AS register,
            user_id AS id,
            nick_name AS name
        FROM (<include refid="getDeptJuniorIds"/>) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN
            (
                SELECT cf.*
                FROM crm_customer_friend AS cf
                <where>
                    <if test="dto.beginTime != null">
                        cf.begin_time >= #{dto.beginTime}
                    </if>
                    <if test="dto.endTime != null">
                        AND cf.begin_time &lt;= #{dto.endTime}
                    </if>
                </where>
            ) AS r ON u.user_id = r.create_by
        <where>
            u.user_id IS NOT NULL
            <if test="dto.userIds != null and dto.userIds.size() > 0">
                AND u.user_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY u.user_id
    </select>

    <select id="getRegisterDept" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT f.customer_id) AS register,
            d.dept_id AS id,
            d.dept_name AS name
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (SELECT f.receive_dept_id,c.customer_id,f.record_date FROM crm_friend AS f
        LEFT JOIN crm_customer_friend AS u ON f.friend_id = u.friend_id
        LEFT JOIN crm_customer AS c ON c.customer_id = u.customer_id
        <where>
            <if test="dto.beginTime != null">
                f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime} AND c.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        ) AS f ON FIND_IN_SET(f.receive_dept_id,d.juniorIds)
        GROUP BY d.dept_id
    </select>

    <select id="getRegisterDeptTimeGroup" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT f.customer_id) AS register
            <include refid="selGroupTime"/>
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN (SELECT f.receive_dept_id,c.customer_id,f.record_date FROM crm_friend AS f
        LEFT JOIN crm_customer_friend AS u ON f.friend_id = u.friend_id
        LEFT JOIN crm_customer AS c ON c.customer_id = u.customer_id
        <where>
            <if test="dto.beginTime != null">
                f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime} AND c.create_time &lt;= #{dto.endTime}
            </if>
        </where>
        ) AS f ON FIND_IN_SET(f.receive_dept_id,d.juniorIds)
        <include refid="groupTime"/>
    </select>

    <select id="getRegisterUserTimeGroup" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT f.customer_id) AS register
            <include refid="selGroupTime"/>
        FROM (
            SELECT
                f.extend_id,
                cf.customer_id,
                f.record_date
            FROM crm_friend AS f
            LEFT JOIN crm_customer_friend AS cf ON f.friend_id = cf.friend_id
            LEFT JOIN crm_customer AS c ON c.customer_id = cf.customer_id
            <where>
                cf.customer_id IS NOT NULL
                <if test="dto.userIds != null and dto.userIds.size() > 0">
                    AND f.extend_id IN
                    <foreach collection="dto.userIds" index="index" item="item" open="("
                             separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.beginTime != null">
                    AND f.record_date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND f.record_date &lt;= #{dto.endTime} AND c.create_time &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS f
        <include refid="groupTime"/>
    </select>

    <select id="selDeptReceiveNumber" resultType="com.yooa.crm.api.domain.vo.ReceiveSettingVo">
        SELECT
            COUNT(DISTINCT f.friend_id) AS customerNumber,
            SUM(DISTINCT CASE WHEN f.extend_id != f.create_by THEN 1 ELSE 0 END) AS actualReceiveNumber,
            d.dept_id AS id
        FROM (
            SELECT d.dept_id,d.dept_name,GROUP_CONCAT(d1.dept_id) AS juniorIds
            FROM yooa_system.sys_dept AS d
            LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id
            GROUP BY d.dept_id
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN crm_friend AS f ON f.extend_id = u.user_id
        WHERE d.dept_id IN
            <foreach collection="ids" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="selUserReceiveNumber" resultType="com.yooa.crm.api.domain.vo.ReceiveSettingVo">
        SELECT
            COUNT(DISTINCT f.friend_id) AS customerNumber,
            SUM(DISTINCT CASE WHEN f.extend_id != f.create_by THEN 1 ELSE 0 END) AS actualReceiveNumber,
            u.user_id AS id
        FROM yooa_system.sys_user AS u
        LEFT JOIN crm_friend AS f ON f.extend_id = u.user_id
        WHERE u.user_id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selFriendEmployeeCustomerIds" resultType="java.lang.Long">
        SELECT
            DISTINCT cf.customer_id
        FROM crm_customer_friend AS cf
        LEFT JOIN crm_friend AS f ON cf.friend_id = f.friend_id
        <where>
            <if test="dto.fansType != null">
                f.fans_type = #{dto.fansType}
            </if>
            <if test="dto.userId != null and dto.userId != 0">
                AND cf.create_by = #{dto.userId}
            </if>
        </where>
    </select>

    <select id="selFriendEmployeeFriendIds" resultType="java.lang.Long">
        SELECT
            DISTINCT c.friend_id
        FROM crm_friend AS f
        <!-- OA用户和好友的关系 -->
        LEFT JOIN (
        SELECT c.friend_id,c.customer_id
        FROM crm_customer_friend AS c
        LEFT JOIN crm_friend AS f ON c.friend_id = f.friend_id
        <where>
            <if test="dto.fansType != null">
                AND f.fans_type = #{dto.fansType}
            </if>
        </where>
        ) AS c ON f.friend_id = c.friend_id
        <!-- 好友和客户的关系 -->
        <where>
            <if test="dto.userId != null and dto.userId != 0">
                f.extend_id = #{dto.userId}
            </if>
        </where>
    </select>

    <select id="selFriendEmployeeMoney" resultType="com.yooa.crm.api.domain.CrmCustomerOrder">
        SELECT
            SUM(o.order_money) AS order_money,
            DATE(o.order_time) AS order_time
        FROM crm_customer AS c
        <!-- PD账号和客户的关系 -->
        LEFT JOIN crm_customer_order AS o ON c.customer_id = o.customer_id AND c.extend_id &lt;= o.py_extend_id
        <!-- 客户和订单的关系 -->
        WHERE
            o.order_time >= #{dto.beginTime}
            AND o.order_time &lt;= #{dto.endTime}
            <if test="dto.customerIds != null and dto.customerIds.size > 0">
                AND c.customer_id IN
                <foreach collection="dto.customerIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.pdUserId != null and dto.pdUserId.size > 0">
                AND c.extend_id IN
                <foreach collection="dto.pdUserId" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY DATE(o.order_time)
        <!-- 按天分组 -->
    </select>

    <select id="selFriendEmployeeFriend" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT f.friend_id) AS friend,
            f.record_date       AS date
        FROM crm_friend AS f
        <where>
            f.extend_id = #{dto.userId}
            <if test="dto.beginTime != null">
                AND f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime}
            </if>
            <if test="dto.fansType != null">
                AND f.fans_type = #{dto.fansType}
            </if>
        </where>
        GROUP BY f.record_date
    </select>

    <select id="selFriendEmployeeRegister" resultType="com.yooa.crm.api.domain.vo.FriendRegisterChargeVo">
        SELECT
            COUNT(DISTINCT c.customer_id) AS register,
            f.record_date                AS date
        FROM crm_friend AS f
        LEFT JOIN crm_customer_friend AS u ON f.friend_id = u.friend_id
        LEFT JOIN crm_customer AS c ON c.customer_id = u.customer_id
        <where>
            f.extend_id = #{dto.userId}
            <if test="dto.beginTime != null">
                AND f.record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND f.record_date &lt;= #{dto.endTime} AND c.create_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.fansType != null">
                AND f.fans_type = #{dto.fansType}
            </if>
        </where>
        GROUP BY f.record_date
    </select>

    <sql id="getDeptJuniorIds">    <!--获取下级部门ID集-->
        SELECT d.dept_id,d.dept_name,GROUP_CONCAT(d1.dept_id) AS juniorIds
        FROM yooa_system.sys_dept AS d
        LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id
        <where>
            d.del_flag = 0
            <if test="dto.deptId != null and dto.deptId != 0">
                AND d.dept_id = #{dto.deptId}
            </if>
            <if test="dto.hierarchy != null and dto.hierarchy != 0">
                AND d.hierarchy = #{dto.hierarchy}
            </if>
            <if test="dto.parentId != null and dto.parentId != 0">
                AND d.parent_id = #{dto.parentId}
            </if>
            <if test="dto.findInSetDeptId != null and dto.findInSetDeptId != 0">
                AND (FIND_IN_SET(#{dto.findInSetDeptId},d.ancestors) or d.dept_id = #{dto.findInSetDeptId})
            </if>
            <if test="dto.deptType != null and dto.deptType != 0">
                AND d.dept_type = #{dto.deptType}
            </if>
        </where>
        GROUP BY d.dept_id
    </sql>

    <sql id="groupTime">
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                GROUP BY DATE_FORMAT(f.record_date, '%Y-%m')
            </when>
            <when test="dto.expType != null and dto.expType == 3">
                GROUP BY DATE_FORMAT(f.record_date, '%Y-%m-%d %H:00:00')
            </when>
            <otherwise>
                GROUP BY DATE(f.record_date)
            </otherwise>
        </choose>
    </sql>

    <sql id="selGroupTime">
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(f.record_date, '%Y-%m'), '-01') AS date
            </when>
            <when test="dto.expType != null and dto.expType == 3">
                ,DATE_FORMAT(f.record_date, '%Y-%m-%d %H:00:00') AS date
            </when>
            <otherwise>
                ,f.record_date AS date
            </otherwise>
        </choose>
    </sql>

    <select id="selFriend" resultType="com.yooa.extend.api.domain.ExtendVermicelli">
        SELECT * FROM yooa_extend.extend_vermicelli WHERE friend_id = #{friendId}
    </select>

    <select id="selFriendIds" resultType="java.lang.Long">
        SELECT e.friend_id
        FROM (SELECT DISTINCT friend_id FROM yooa_extend.extend_vermicelli WHERE remark like '%新绑定时补录的粉丝登记%') e
                 LEFT JOIN crm_customer_friend cf ON e.friend_id = cf.friend_id
        GROUP BY cf.friend_id
        HAVING COUNT(cf.friend_id) = 1
    </select>
    <select id="getCustomerIdList" resultType="java.lang.Long">
        SELECT cf.customer_id FROM crm_friend f
        INNER JOIN crm_customer_friend cf on f.friend_id = cf.friend_id
        WHERE f.friend_id = #{friendId}
    </select>

    <select id="selReceiveCommonFriend" resultType="com.yooa.crm.api.domain.vo.CrmCommonFriendVo">
        select
        f.friend_name name,
        f.friend_id friendId,
        cf.customer_id customerId,
        f.sex,
        f.age,
        f.area,
        f.language,
        f.demand,
        f.income_level incomeLevel,
        f.work_type workType,
        v.fans_type maxFansType,
        f.lose_time loseTime,
        f.record_date recordDate,
        f.create_time createTime
        from crm_friend f
        LEFT JOIN crm_customer_friend cf on cf.friend_id = f.friend_id
        LEFT JOIN yooa_system.sys_user u ON u.user_id = f.extend_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        LEFT JOIN (select friend_id,extend_id,fans_type,ROW_NUMBER() OVER( PARTITION BY friend_id ORDER BY fans_type DESC) rn from yooa_extend.extend_vermicelli)v on v.friend_id = f.friend_id and f.extend_id = v.extend_id and v.rn = 1
        <where>
            f.status = 0
            AND f.receive_number > 1
            AND f.contact_phone IS NOT NULL
            AND f.contact_phone != ''
            AND f.extend_id = #{userId}
            <if test="query.area != null and query.area != ''">
                AND f.area = #{query.area}
            </if>
            <if test="query.workType != null and query.workType != ''">
                AND f.work_type = #{query.workType}
            </if>
            <if test="query.beginAge != null">
                AND f.age >= #{query.beginAge}
            </if>
            <if test="query.endAge != null">
                AND f.age &lt;= #{query.endAge}
            </if>
            <if test="query.sex != null and query.sex != ''">
                AND f.sex = #{query.sex}
            </if>
            <if test="query.language != null and query.language != ''">
                AND f.language = #{query.language}
            </if>
            <if test="query.recordDate != null">
                AND (f.record_date BETWEEN #{query.recordDate[0]} AND #{query.recordDate[1]})
            </if>
            <if test="query.loseDate != null">
                AND (f.lose_time BETWEEN #{query.LoseDate[0]} AND #{query.LoseDate[1]})
            </if>
            <if test="query.keyWord != null and query.keyWord != ''">
                AND f.friend_name like CONCAT('%',#{query.keyWord},'%')
                OR  f.contact_phone = #{query.keyWord}
            </if>
        </where>
        ORDER BY f.friend_id DESC
    </select>

    <select id="pollingFriendList" resultType="com.yooa.crm.api.domain.vo.PollingFriendVo">
        select
        f.*,
        d0.dict_label   AS sexName,
        d1.dict_label  AS language,
        d2.dict_label  AS fansType,
        d3.dict_label  AS area,
        d4.dict_label  AS workType,
        d5.dict_label  AS matrimony,
        d6.dict_label  AS demand,
        IFNULL(d7.dict_label, '') AS pollingApiChannelName,
        IFNULL(d8.dict_label, '') AS contactModeName,
        cf.customer_id customerId,
        u1.nick_name AS pitcherName,
        u2.nick_name AS extendName,
        IFNULL(maincfc.channel_name, '') AS mainChanneName,
        IFNULL(subcfc.channel_name, '') AS subChanneName,
        re.review_remark AS pollingRemark,
        fpr.state AS state
        FROM crm_friend f
        LEFT JOIN (
        SELECT
        *
        FROM
        (
        SELECT
        friend_id,
        review_remark,
        create_time,
        state,
        ROW_NUMBER() OVER ( PARTITION BY friend_id ORDER BY create_time DESC ) AS rn
        FROM
        crm_friend_polling_review
        ) t
        WHERE
        rn = 1
        ) fpr ON fpr.friend_id = f.friend_id
        LEFT JOIN (
        SELECT
        *
        FROM
        (
        SELECT
        friend_id,
        review_remark,
        create_time,
        state,
        ROW_NUMBER() OVER ( PARTITION BY friend_id ORDER BY create_time DESC ) AS rn
        FROM
        crm_friend_polling_review
        where review_remark is not null
        ) t
        WHERE
        rn = 1
        ) re ON re.friend_id = f.friend_id
        LEFT JOIN crm_customer_friend cf on cf.friend_id = f.friend_id
        LEFT JOIN yooa_system.sys_user u1 ON f.pitcher_id = u1.user_id
        LEFT JOIN yooa_system.sys_user u2 ON f.extend_id = u2.user_id
        LEFT JOIN crm_friend_channel  maincfc on f.main_channel_id = maincfc.channel_id
        LEFT JOIN crm_friend_channel  subcfc on f.sub_channel_id = subcfc.channel_id
        LEFT JOIN yooa_system.sys_dict_data d0 ON d0.dict_type = 'common_sex_type' AND d0.dict_value = f.sex
        LEFT JOIN yooa_system.sys_dict_data d1 ON d1.dict_type = 'sys_friend_language_type' AND d1.dict_value = f.language
        LEFT JOIN yooa_system.sys_dict_data d2 ON d2.dict_type = 'crm_friend_fans_type' AND d2.dict_value = f.fans_type
        LEFT JOIN yooa_system.sys_dict_data d3 ON d3.dict_type = 'crm_friend_area_type' AND d3.dict_value = f.area
        LEFT JOIN yooa_system.sys_dict_data d4 ON d4.dict_type = 'crm_friend_work_type' AND d4.dict_value = f.work_type
        LEFT JOIN yooa_system.sys_dict_data d5 ON d5.dict_type = 'common_marital_status' AND d5.dict_value = f.matrimony
        LEFT JOIN yooa_system.sys_dict_data d6 ON d6.dict_type = 'crm_friend_emotion_type' AND d6.dict_value = f.demand
        LEFT JOIN yooa_system.sys_dict_data d7 ON d7.dict_type = 'crm_polling_channel_name' AND d7.dict_value = f.polling_api_channel
        LEFT JOIN yooa_system.sys_dict_data d8 ON d8.dict_type = 'crm_friend_contact_type' AND d8.dict_value = f.contact_mode
        <if test="query.businessType == 'pitcher'">
            LEFT JOIN yooa_system.sys_user u ON f.pitcher_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        </if>
        <if test="query.businessType == 'extend'">
            LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        </if>
        <where>
            f.type = 2
            ${query.params.dataScope}
            <if test="query.pollingType != null and query.pollingType != ''">
                and f.polling_type = #{query.pollingType}
            </if>
            <if test="query.pollingApiChannel != null and query.pollingApiChannel != ''">
                and f.polling_api_channel = #{query.pollingApiChannel}
            </if>
            <if test="query.subChannelId != null ">
                AND f.sub_channel_id = #{query.subChannelId}
                AND f.sub_channel_id IS NOT NULL
            </if>
            <if test="query.recordDate != null">
                AND (f.record_date BETWEEN #{query.recordDate[0]} AND #{query.recordDate[1]})
            </if>
            <if test="query.searchBox">
                AND (f.friend_name LIKE concat('%', #{query.searchBox}, '%')
                OR f.polling_api_phone LIKE concat('%', #{query.searchBox}, '%')
                OR u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                OR u2.nick_name LIKE concat('%', #{query.searchBox}, '%'))
            </if>
            <if test="query.state != null">
                and fpr.state = #{query.state}
            </if>
            ORDER BY f.create_time DESC
        </where>
    </select>
    <select id="pollingInvalidList" resultType="com.yooa.crm.api.domain.vo.PollingFriendInvalidVo">
        select
        f.*,
        d0.dict_label   AS sexName,
        d1.dict_label  AS languageName,
        d2.dict_label  AS fansTypeName,
        d3.dict_label  AS areaName,
        d4.dict_label  AS workTypeName,
        d5.dict_label  AS matrimonyName,
        IFNULL(d6.dict_label, '')  AS demandName,
        IFNULL(d7.dict_label, '') AS pollingApiChannelName,
        IFNULL(d8.dict_label, '') AS contactModeName,
        cf.customer_id customerId,
        u1.nick_name AS pitcherName,
        u2.nick_name AS extendName,
        IFNULL(maincfc.channel_name, '') AS mainChanneName,
        IFNULL(subcfc.channel_name, '') AS subChanneName,
        fpr.state AS state,
        re.review_remark reviewRemark
        from  crm_friend f
        LEFT JOIN (
        SELECT
        *
        FROM
        (
        SELECT
        friend_id,
        review_remark,
        create_time,
        state,
        ROW_NUMBER() OVER ( PARTITION BY friend_id ORDER BY create_time DESC ) AS rn
        FROM
        crm_friend_polling_review
        ) t
        WHERE
        rn = 1
        ) fpr ON fpr.friend_id = f.friend_id
        LEFT JOIN (
        SELECT
        *
        FROM
        (
        SELECT
        friend_id,
        review_remark,
        create_time,
        state,
        ROW_NUMBER() OVER ( PARTITION BY friend_id ORDER BY create_time DESC ) AS rn
        FROM
        crm_friend_polling_review
        where review_remark is not null
        ) t
        WHERE
        rn = 1
        ) re ON re.friend_id = f.friend_id
        LEFT JOIN crm_customer_friend cf on cf.friend_id = f.friend_id
        LEFT JOIN yooa_system.sys_user u1 ON f.pitcher_id = u1.user_id
        LEFT JOIN yooa_system.sys_user u2 ON f.extend_id = u2.user_id
        LEFT JOIN crm_friend_channel  maincfc on f.main_channel_id = maincfc.channel_id
        LEFT JOIN crm_friend_channel  subcfc on f.sub_channel_id = subcfc.channel_id
        LEFT JOIN yooa_system.sys_dict_data d0 ON d0.dict_type = 'common_sex_type' AND d0.dict_value = f.sex
        LEFT JOIN yooa_system.sys_dict_data d1 ON d1.dict_type = 'sys_friend_language_type' AND d1.dict_value = f.language
        LEFT JOIN yooa_system.sys_dict_data d2 ON d2.dict_type = 'crm_friend_fans_type' AND d2.dict_value = f.fans_type
        LEFT JOIN yooa_system.sys_dict_data d3 ON d3.dict_type = 'crm_friend_area_type' AND d3.dict_value = f.area
        LEFT JOIN yooa_system.sys_dict_data d4 ON d4.dict_type = 'crm_friend_work_type' AND d4.dict_value = f.work_type
        LEFT JOIN yooa_system.sys_dict_data d5 ON d5.dict_type = 'common_marital_status' AND d5.dict_value = f.matrimony
        LEFT JOIN yooa_system.sys_dict_data d6 ON d6.dict_type = 'crm_friend_emotion_type' AND d6.dict_value = f.demand
        LEFT JOIN yooa_system.sys_dict_data d7 ON d7.dict_type = 'crm_polling_channel_name' AND d7.dict_value = f.polling_api_channel
        LEFT JOIN yooa_system.sys_dict_data d8 ON d8.dict_type = 'crm_friend_contact_type' AND d8.dict_value = f.contact_mode

        <if test="query.businessType == 'pitcher'">
            LEFT JOIN yooa_system.sys_user u ON f.pitcher_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        </if>
        <if test="query.businessType == 'extend'">
            LEFT JOIN yooa_system.sys_user u ON f.extend_id = u.user_id
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        </if>

        <where>
            f.type = 2 and fpr.friend_id is not null and fpr.state != 4
            ${query.params.dataScope}
            <if test="query.pollingType != null and query.pollingType != ''">
                and f.polling_type = #{query.pollingType}
            </if>
            <if test="query.pollingApiChannel != null and query.pollingApiChannel != ''">
                and f.polling_api_channel = #{query.pollingApiChannel}
            </if>
            <if test="query.subChannelId != null ">
                AND f.sub_channel_id = #{query.subChannelId}
                AND f.sub_channel_id IS NOT NULL
            </if>
            <if test="query.recordDate != null">
                AND (f.record_date BETWEEN #{query.recordDate[0]} AND #{query.recordDate[1]})
            </if>
            <if test="query.friendName != null and query.friendName != ''">
                AND f.friend_name LIKE concat('%', #{query.friendName}, '%'))
            </if>
            <if test="query.searchBox">
                AND (f.friend_name LIKE concat('%', #{query.searchBox}, '%')
                OR f.polling_api_phone LIKE concat('%', #{query.searchBox}, '%')
                OR u1.nick_name LIKE concat('%', #{query.searchBox}, '%')
                OR u2.nick_name LIKE concat('%', #{query.searchBox}, '%'))
            </if>
            <if test="query.state != null">
                and fpr.state = #{query.state}
            </if>
            ORDER BY fpr.create_time DESC
        </where>

    </select>
    <select id="statisticsList" resultType="com.yooa.crm.api.domain.vo.PollingStatisticsVo">
            select * from(
        SELECT
            a.pitcherName,
            a.pitcherId,
            a.extendName,
            a.extendId,
        SUM(a.total) total,
        SUM(a.registerTotal) registerTotal,
        SUM( a.registerTotal ) / COUNT( a.friend_id ) * 100 registerRate,
        SUM( CASE WHEN a.polling_type = 1 THEN 1 ELSE 0 END ) AS effectiveTotal ,
        SUM( CASE WHEN a.polling_type = 1 THEN 1 ELSE 0 END ) / COUNT( a.friend_id ) *100 AS effectiveRate
        FROM (SELECT cf.polling_type,
        cf.friend_id,
        cf.extend_id,
        cf.pitcher_id,
        <if test="query.businessType == 'pitcher'">
            u.nick_name                                      pitcherName,
            u.user_id                                      pitcherId,
            u1.nick_name                                      extendName,
            u1.user_id                                      extendId,
        </if>
        <if test="query.businessType == 'extend'">
            u.nick_name                                      extendName,
            u.user_id                                      extendId,
            u1.nick_name                                      pitcherName,
            u1.user_id                                      pitcherId,
        </if>
        COUNT(pc.polling_api_user_id)                 AS total,
        ( CASE WHEN cf_reg.friend_id is not null THEN 1 ELSE 0 END ) AS registerTotal
        FROM crm_friend cf
        <if test="query.businessType == 'pitcher'">
            LEFT JOIN yooa_system.sys_user u ON cf.pitcher_id = u.user_id
            LEFT JOIN yooa_system.sys_user u1 ON cf.extend_id = u1.user_id
        </if>
        <if test="query.businessType == 'extend'">
            LEFT JOIN yooa_system.sys_user u ON cf.extend_id = u.user_id
            LEFT JOIN yooa_system.sys_user u1 ON cf.pitcher_id = u1.user_id
        </if>
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN crm_friend_polling_chat pc ON cf.polling_api_user_id = pc.polling_api_user_id
        LEFT JOIN crm_customer_friend cf_reg ON cf.friend_id = cf_reg.friend_id
        <where>
            cf.type = 2 AND u.user_id IS NOT NULL
            ${query.params.dataScope}
            <if test="query.recordDate != null">
                AND (cf.record_date BETWEEN #{query.recordDate[0]} AND #{query.recordDate[1]})
            </if>
            <if test="query.searchBox != null and query.searchBox != ''">
                AND u.nick_name LIKE concat('%', #{query.searchBox}, '%')
            </if>
        </where>

        GROUP BY cf.create_time,
        cf.polling_type,
        cf.friend_id,
        <if test="query.businessType == 'pitcher'">
            cf.pitcher_id,
        </if>
        <if test="query.businessType == 'extend'">
            cf.extend_id,
        </if>
        u.nick_name
        ORDER BY cf.create_time DESC
        ) a
        GROUP BY
        a.extend_id
        ORDER BY a.extend_id
        ) b

        <where>
            1 = 1
            <if test="query.chatStart != null and query.chatStart != ''">
                AND b.total >= #{query.chatStart}
            </if>
            <if test="query.chatEnd != null and query.chatEnd != ''">
                AND b.total &lt;= #{query.chatEnd}
            </if>
            <if test="query.invalidStart != null and query.invalidStart != ''">
                AND b.effectiveTotal >= #{query.invalidStart}
            </if>
            <if test="query.invalidEnd != null and query.invalidEnd != ''">
                AND b.effectiveTotal &lt;= #{query.invalidEnd}
            </if>
            <if test="query.invalidRateStart != null and query.invalidRateStart != ''">
                AND b.effectiveRate >= #{query.invalidRateStart}
            </if>
            <if test="query.invalidRateEnd != null and query.invalidRateEnd != ''">
                AND b.effectiveRate &lt;= #{query.invalidRateEnd}
            </if>
            <if test="query.registeredStart != null and query.registeredStart != ''">
                AND b.registerTotal >= #{query.registeredStart}
            </if>
            <if test="query.registeredEnd != null and query.registeredEnd != ''">
                AND b.registerTotal &lt;= #{query.registeredEnd}
            </if>
            <if test="query.registrationRateStart != null and query.registrationRateStart != ''">
                AND b.registrationRate >= #{query.registrationRateStart}
            </if>
            <if test="query.registrationRateEnd != null and query.registrationRateEnd != ''">
                AND b.registrationRate &lt;= #{query.registrationRateEnd}
            </if>

        </where>
    </select>
    <select id="getChatSequenceList" resultType="com.yooa.crm.api.domain.vo.FriendSequenceIdVo">
        SELECT
            cfpc.sequence_id sequenceId,
            cf.*
        FROM
            crm_friend cf
                LEFT JOIN (
                SELECT
                    *
                FROM
                    ( SELECT polling_api_user_id, sequence_id, ROW_NUMBER() OVER ( PARTITION BY polling_api_user_id ORDER BY sequence_id DESC ) AS rn FROM crm_friend_polling_chat ) t
                WHERE
                    rn = 1
            ) cfpc ON cf.polling_api_user_id = cfpc.polling_api_user_id
        WHERE
            cf.type = 2
    </select>

    <select id="getVipRechargeOrder" resultType="com.yooa.crm.api.domain.vo.VipRechargeOrderVo">
        SELECT
            co.customer_id customerId,
            co.order_no orderNo,
            co.pd_third_order_no thirdOrderNo,
            co.order_time orderTime,
            co.order_money amount,
            co.payment_type paymentType,
            u.nick_name serveName,
            d.dept_name serveDeptName,
            d.ancestors_names serveAncestorsNames
        FROM
            crm_customer_order co
                INNER JOIN yooa_system.sys_user u on u.user_id = co.serve_id
                LEFT JOIN yooa_system.sys_dept d on u.dept_id = d.dept_id
        WHERE 1 = 1
            ${query.params.dataScope}
        <if test="query.minAmount != null">
            AND co.order_money >= #{query.minAmount}
        </if>
        <if test="query.maxAmount != null">
            AND co.order_money &lt;= #{query.minAmount}
        </if>
        <if test="query.queryId != null and query.queryId != ''">
            AND (co.customer_id = #{query.queryId}
            OR co.order_no = #{query.queryId}
            OR co.pd_third_order_no = #{query.queryId})
        </if>

    </select>

    <delete id="delFriend">
        DELETE
        FROM yooa_extend.extend_vermicelli
        WHERE friend_id = #{friendId}
    </delete>

</mapper>
