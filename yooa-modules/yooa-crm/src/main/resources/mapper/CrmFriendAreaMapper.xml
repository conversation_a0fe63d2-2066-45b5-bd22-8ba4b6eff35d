<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmFriendAreaMapper">

    <resultMap type="com.yooa.crm.api.domain.CrmFriendArea" id="CrmFriendAreaResult">
        <result property="id" column="id"/>
        <result property="area" column="area"/>
        <result property="parentId" column="parent_id"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectCrmFriendAreaVo">
        select id, area, parent_id, sort, create_by, create_time, update_by, update_time, remark
        from crm_friend_area
    </sql>

    <select id="selectFriendAreaList" parameterType="com.yooa.crm.api.domain.CrmFriendArea" resultMap="CrmFriendAreaResult">
        <include refid="selectCrmFriendAreaVo"/>
        <where>
            <if test="area != null and area != ''">
                AND area like concat('%', #{area}, '%')
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
        </where>
        order by sort asc, id asc
    </select>

    <select id="selectFriendAreaByParentId" resultMap="CrmFriendAreaResult">
        <include refid="selectCrmFriendAreaVo"/>
        where parent_id = #{parentId}
        order by sort asc, id asc
    </select>

    <select id="selectMainAreas" resultMap="CrmFriendAreaResult">
        <include refid="selectCrmFriendAreaVo"/>
        where parent_id = 0
        order by sort asc, id asc
    </select>

    <select id="selectSubAreas" resultMap="CrmFriendAreaResult">
        <include refid="selectCrmFriendAreaVo"/>
        where parent_id != 0
        order by sort asc, id asc
    </select>

</mapper>
